// HeyGen API Types

export interface HeyGenAvatar {
	avatar_id: string;
	avatar_name: string;
	gender: string;
	preview_image_url: string;
	preview_video_url: string;
	premium: boolean;
}

export interface HeyGenTalkingPhoto {
	talking_photo_id: string;
	talking_photo_name: string;
	preview_image_url: string;
}

export interface HeyGenAvatarsResponse {
	avatars: HeyGenAvatar[];
	talking_photos: HeyGenTalkingPhoto[];
}

export interface HeyGenVideoGenerationRequest {
	title?: string;
	callback_id?: string;
	video_inputs: HeyGenVideoInput[];
	dimension: HeyGenDimension;
	folder_id?: string;
	callback_url?: string;
	caption?: boolean;
}

export interface HeyGenVideoInput {
	character?: HeyGenAvatarSettings | HeyGenTalkingPhotoSettings;
	voice: HeyGenTextVoiceSettings | HeyGenAudioVoiceSettings | HeyGenSilenceVoiceSettings;
	background?: HeyGenColorBackground | HeyGenImageBackground | HeyGenVideoBackground;
}

export interface HeyGenAvatarSettings {
	type: "avatar";
	avatar_id: string;
	scale?: number;
	avatar_style?: "circle" | "normal" | "closeUp";
	offset?: HeyGenOffset;
	matting?: boolean;
	circle_background_color?: string;
}

export interface HeyGenTalkingPhotoSettings {
	type: "talking_photo";
	talking_photo_id: string;
	scale?: number;
	talking_photo_style?: "square" | "circle";
	offset?: HeyGenOffset;
	talking_style?: "stable" | "expressive";
	expression?: "default" | "happy";
	super_resolution?: boolean;
	matting?: boolean;
	circle_background_color?: string;
}

export interface HeyGenOffset {
	x: number;
	y: number;
}

export interface HeyGenTextVoiceSettings {
	type: "text";
	voice_id: string;
	input_text: string;
	speed?: number;
	pitch?: number;
	emotion?: string;
	locale?: string;
}

export interface HeyGenAudioVoiceSettings {
	type: "audio";
	audio_url?: string;
	audio_asset_id?: string;
}

export interface HeyGenSilenceVoiceSettings {
	type: "silence";
	duration: number;
}

export interface HeyGenColorBackground {
	type: "color";
	value: string;
}

export interface HeyGenImageBackground {
	type: "image";
	url?: string;
	image_asset_id?: string;
	fit?: "cover" | "crop" | "contain" | "none";
}

export interface HeyGenVideoBackground {
	type: "video";
	url?: string;
	video_asset_id?: string;
	play_style: "fit_to_scene" | "freeze" | "loop" | "once";
	fit?: "cover" | "crop" | "contain" | "none";
}

export interface HeyGenDimension {
	width: number;
	height: number;
}

export interface HeyGenVideoGenerationResponse {
	video_id: string;
}

export interface HeyGenVideoStatus {
	video_id: string;
	status: "pending" | "processing" | "completed" | "failed";
	video_url?: string;
	thumbnail_url?: string;
	duration?: number;
	error?: string;
}
