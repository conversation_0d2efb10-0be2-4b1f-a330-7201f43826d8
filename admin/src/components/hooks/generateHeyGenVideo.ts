import axios from "axios";
import { HeyGenVideoGenerationRequest, HeyGenVideoGenerationResponse } from "@src/types/heygen";

const generateHeyGenVideo = async (
	request: HeyGenVideoGenerationRequest
): Promise<{ data: HeyGenVideoGenerationResponse | null; error: any }> => {
	try {
		const response = await axios.request({
			url: "https://api.heygen.com/v2/video/generate",
			method: "POST",
			data: request,
			headers: {
				Accept: "application/json",
				"Content-Type": "application/json",
				"X-Api-Key": "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ=="
			}
		});

		return { data: response.data, error: null };
	} catch (error) {
		console.error("Error generating HeyGen video:", error);
		return { data: null, error };
	}
};

export default generateHeyGenVideo;
