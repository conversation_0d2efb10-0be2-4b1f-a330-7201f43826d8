import axios from "axios";
import { HeyGenVideoStatus } from "@src/types/heygen";

const getHeyGenVideoStatus = async (
    videoId: string
): Promise<{ data: HeyGenVideoStatus | null; error: any }> => {
    try {
        const response = await axios.request({
            //url: `https://api.heygen.com/v1/video_status/${videoId}`,
						url: `https://api.heygen.com/v1/video_status.get?video_id=${videoId}`,
            method: "GET",
            headers: {
                Accept: "application/json",
                "X-Api-Key": "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ=="
            }
        });

        return { data: response.data, error: null };
    } catch (error) {
        console.error("Error fetching HeyGen video status:", error);
        return { data: null, error };
    }
};

export default getHeyGenVideoStatus;