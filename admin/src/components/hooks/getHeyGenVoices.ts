import axios from "axios";

interface HeyGenVoice {
    voice_id: string;
    name: string;
    language: string;
    gender: string;
    preview_audio: string;
}

const getHeyGenVoices = async (): Promise<{ data: { voices: HeyGenVoice[] } | null; error: any }> => {
    try {
        const response = await axios.request({
            url: "https://api.heygen.com/v2/voices",
            method: "GET",
            headers: {
                Accept: "application/json",
                "X-Api-Key": "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ=="
            }
        });

        return { data: response.data.data, error: null };
    } catch (error) {
        console.error("Error fetching HeyGen voices:", error);
        return { data: null, error };
    }
};

export default getHeyGenVoices;