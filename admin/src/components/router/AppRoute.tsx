import React, { useEffect } from "react";
import { RouteProps, Navigate } from "react-router-dom";
import Session<PERSON>anagerSingleton from "@src/components/events/SessionManagerSingleton";
import { isLoggedInState } from "../authentication/state";
import { useRecoilState } from "recoil";
import Dashboard from "../pages/Dashboard";
import Videos from "../pages/Videos";
import Collections from "../pages/Collections";
import AddCompany from "../pages/AddCompany";
import CreateVideo from "../pages/CreateVideo";
import EditVideo from "../pages/EditVideo";
import AccountSettings from "../pages/AccountSettings";
import Company from "../pages/Company";
import SignIn from "../pages/SignIn";
import ResetPassword from "../pages/ResetPassword";
import ForgotPassword from "../pages/ForgotPassword";
import VerifyEmail from "../pages/VerifyEmail";
import NotFound from "../pages/NotFound";
import Profile from "../pages/Profile";
import Plans from "../pages/Plans";
import CreateCollection from "../pages/CreateCollection";
import EditCollection from "../pages/EditCollection";
import VideoLibrary from "../pages/VideoLibraryPage";
import VideoAvatars from "../pages/VideoAvatarsPage";
import SignInEmail from "../pages/SignInEmail";
import VerifyEmailSignIn from "../pages/VerifyEmailSignIn";
import AddAccount from "../pages/AddAccount";
import SignInIntuit from "../pages/SignInIntuit";
import CreateAccountEmail from "../pages/CreateAccountEmail";
import CreateAccount from "../pages/CreateAccount";
import { useLogRocket } from "../hooks/useLogRocket";

export interface AppRoute {
	element: JSX.Element;
	displayName: string;
	path: string;
	isPublic?: boolean;
}

const publicSignUp = process.env.PUBLIC_SIGN_UP === "true";
const intuitSignIn = process.env.INTUIT_SIGN_IN === "true";
const plansPage = process.env.SHOW_PLANS_PAGE === "true";

export const routes: AppRoute[] = [
	{
		element: <Dashboard />,
		displayName: "Dashboard",
		path: "",
		isPublic: false
	},
	{
		element: <Videos />,
		displayName: "Videos",
		path: "/videos",
		isPublic: false
	},
	{
		element: <Collections />,
		displayName: "Collections",
		path: "collections",
		isPublic: false
	},
	{
		element: <AddCompany />,
		displayName: "AddCompany",
		path: "add-company",
		isPublic: false
	},
	{
		element: <CreateVideo />,
		displayName: "CreateVideo",
		path: "create-video",
		isPublic: false
	},
	{
		element: <EditVideo />,
		displayName: "EditVideo",
		path: "edit-video/:id",
		isPublic: false
	},
	{
		element: <AccountSettings />,
		displayName: "AccountSettings",
		path: "account-settings",
		isPublic: false
	},
	{
		element: <CreateCollection />,
		displayName: "CreateCollection",
		path: "create-collection",
		isPublic: false
	},
	{
		element: <EditCollection />,
		displayName: "EditCollection",
		path: "edit-collection/:id",
		isPublic: false
	},
	{
		element: <Profile />,
		displayName: "Profile",
		path: "profile",
		isPublic: false
	},
	...(plansPage
		? [
			{
				element: <Plans />,
				displayName: "Plans",
				path: "plans-pricing",
				isPublic: false
			}
		]
		: []),
	{
		element: <Company />,
		displayName: "Company",
		path: "company",
		isPublic: false
	},
	{
		element: <SignIn />,
		displayName: "SignIn",
		path: "sign-in/password",
		isPublic: true
	},
	{
		element: <SignInEmail />,
		displayName: "SignInEmail",
		path: "sign-in/email",
		isPublic: true
	},
	{
		element: <AddAccount />,
		displayName: "AddAccount",
		path: "add-account",
		isPublic: false
	},
	{
		element: <CreateAccount />,
		displayName: "CreateAccountPassword",
		path: "create-account/password",
		isPublic: true
	},
	...(publicSignUp
		? [
			{
				element: <CreateAccountEmail />,
				displayName: "CreateAccount",
				path: "create-account",
				isPublic: true
			}
		]
		: []),
	...(intuitSignIn
		? [
			{
				element: <SignInIntuit />,
				displayName: "SignInIntuit",
				path: "sign-in/intuit",
				isPublic: true
			}
		]
		: []),
	{
		element: <ResetPassword />,
		displayName: "ResetPassword",
		path: "reset-password",
		isPublic: true
	},
	{
		element: <ForgotPassword />,
		displayName: "ForgotPassword",
		path: "forgot-password",
		isPublic: true
	},
	{
		element: <VerifyEmail />,
		displayName: "VerifyEmail",
		path: "confirm-verification",
		isPublic: true
	},
	{
		element: <VerifyEmailSignIn />,
		displayName: "VerifyEmailSignIn",
		path: "verify-sign-in",
		isPublic: true
	},
	{
		element: <NotFound />,
		displayName: "NotFound",
		path: "*",
		isPublic: true
	},
	{
		element: <VideoLibrary />,
		displayName: "VideoLibrary",
		path: "video-library",
		isPublic: false
	},
	{
		element: <VideoAvatars />,
		displayName: "VideoAvatars",
		path: "video-avatars",
		isPublic: false
	},
];

type AppRouteProps = RouteProps & {
	Component: JSX.Element;
	displayName: string;
	isPublic?: boolean;
};

export const AppRoute: React.FC<AppRouteProps> = ({ Component, displayName, isPublic }) => {
	const [isLoggedIn, setIsLoggedIn] = useRecoilState(isLoggedInState);
	const accountToken = sessionStorage.getItem("token");

	useLogRocket();

	useEffect(() => {
		(async () => {
			// set user session
			const sessionManager = SessionManagerSingleton.getInstance();
			await sessionManager.initSession();

			const accessToken = localStorage.getItem("accessToken");
			const refreshToken = localStorage.getItem("refreshToken");
			if (!accessToken || !refreshToken) {
				setIsLoggedIn(false);
			}
		})();
	}, [setIsLoggedIn]);

	if (isPublic && isLoggedIn) {
		if (displayName === "VerifyEmail" || displayName === "ForgotPassword" || displayName === "VerifyEmailSignIn") {
			return Component;
		}
		if (!accountToken) {
			return <Navigate to="/company" />;
		}

		return <Navigate to="/" />;
	}
	if (!isPublic && !isLoggedIn) {
		return <Navigate to="/sign-in" />;
	}

	if (displayName === "AddCompany" || displayName === "Company" || accountToken || isPublic) {
		return Component;
	}

	localStorage.setItem("setToken", "true");
	sessionStorage.setItem("redirectAfterCompanySelection", window.location.pathname + window.location.hash);
	return <Navigate to="/company" />;
};
