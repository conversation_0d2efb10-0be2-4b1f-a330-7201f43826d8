/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import axios from "axios";
import { ConfirmationBoxWrapper, HeadingText, SubheadingText } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import {
	PageBody,
	PageSection,
	MainButton,
	VideoGrid,
	VideoTile,
	VideoInfo,
	VideoPoster,
	FlexCol
} from "@src/styles/components";
import { getErrorString } from "../hooks/getErrorString";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import Lottie from "lottie-react";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";

interface Props {
	saveChanges: boolean;
	setSaveChanges: (value: boolean) => void;
	navigationUrl: string;
	showConfirmLeavingModal: boolean;
	setShowConfirmLeavingModal: (value: boolean) => void;
	hideCreateVideo: boolean;
}

// eslint-disable-next-line max-lines-per-function
const VideoLibrary: React.FC<Props> = ({
	navigationUrl,
	saveChanges,
	setSaveChanges,
	showConfirmLeavingModal,
	setShowConfirmLeavingModal,
	hideCreateVideo
}) => {
	const translation = useTranslation();
	const navigate = useNavigate();
	const { apiRetryHandler } = useTokenCheck();
	const [uploadQueue, setUploadQueue] = useState<File[]>([]);
	const [igProcessingList, setIgProcessingList] = useState<VideoEncodeResponse[]>([]);
	const isMonitoring = useRef(false);
	const [videos, setVideos] = useState<Video[]>([]);
	const [cursor, setCursor] = useState<string | null>(null);
	const [showMoreButtonVisible, setShowMoreButtonVisible] = useState(false);
	const [showDeletionModal, setShowDeletionModal] = useState(false);
	const [deleteVideoId, setDeleteVideoId] = useState("");
	const [showVideoPreviewModal, setShowVideoPreviewModal] = useState(false);
	const [videoPreviewUrl, setVideoPreviewUrl] = useState("");
	const [goToPrevPage, setGoToPrevPage] = useState(false);
	const [editVideoError, setEditVideoError] = useState("");

	// Get all videos in library
	const fetchVideos = async () => {
		const { data, error }: {data: {videos: Video[]}; error: any} = await apiRetryHandler(
			async () => await getVideoFiles(cursor)
		);
		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setEditVideoError(errorText);
		} else {
			const videoFiles = data.videos;
			setVideos((prevVideos) => [...prevVideos, ...videoFiles]);
			if (data.videos.length > 0) {
				setCursor(data.videos[data.videos.length - 1]._id);
				setShowMoreButtonVisible(true);
			} else {
				setShowMoreButtonVisible(false);
			}
		}
	};

	const fetchVideoJobs = async () => {
		const { data, error }: {data: {jobs?: VideoEncodeResponse[]}; error: any} = await apiRetryHandler(
			async () => await getVideoJobs()
		);
		if (error) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setEditVideoError(errorText);
		} else {
			if (data?.jobs !== undefined && data?.jobs.length > 0) {
				const processingVideos = data.jobs;
				setIgProcessingList((prev) => [...prev, ...processingVideos]);
			}
		}
	};


		const getListAvatars = async () => {


			// curl --request GET \
			//  --url https://api.heygen.com/v2/avatars \
			//  --header 'Accept: application/json' \
			//  --header 'X-Api-Key: <your-api-key>'

			const getAvatars = await axios.request({
				url: `https://api.heygen.com/v2/avatars`,
				method: "GET",
				headers: {
					Accept: "application/json",
					"X-Api-Key": "Y2JkYWI4NWNjMDMwNGQ1YmFhZGQ3NDY3NWY2MDA0MDEtMTc1MTkxNTM1MQ=="
					}
				});

				console.log(getAvatars);

					// const returnObject = await axios.request({
					// 	url: `${API_ENDPOINT}/api/auth/signup/enterprise`,
					// 	method: "POST",
					// 	data: JSON.stringify(requestObject),
					// 	headers: {
					// 		Authorization: `Bearer ${accessToken}`,
					// 		"content-type": "application/json",
					// 		"x-api-version": API_VERSION
					// 	}
					// });


		};

	useEffect(() => {
		getListAvatars();
	}, []);

	useEffect(() => {
		if (editVideoError) {
			const resetErrorMsg = async () => {
				const errorMsg = editVideoError;
				await new Promise((resolve) => setTimeout(resolve, 3000));
				if (editVideoError === errorMsg) {
					setEditVideoError("");
				}
			};
			resetErrorMsg();
		}
	}, [editVideoError, setEditVideoError]);

	// Handles file selection and adds accepted files to upload queue
	const { getRootProps: getVideoProps } = useDropzone({
		accept: {
			"video/*": []
		},
		onDrop: (acceptedFiles) => {
			const fileSizeLimitMB = 512;
			if (
				acceptedFiles.length &&
				acceptedFiles.every((acceptedFile) => acceptedFile.size < fileSizeLimitMB * 1000 * 1000)
			) {
				(async () => {
					setUploadQueue((prev) => [...prev, ...acceptedFiles]);
				})();
			} else {
				if (!acceptedFiles.length) {
					setEditVideoError(translation.errors.videoLibraryWrongFileType);
				} else {
					setEditVideoError(translation.errors.videoLibraryFileTooLarge);
				}
			}
		}
	});

	// Tracks upload queue to upload files sequentially and places them in processing list after upload
	useEffect(() => {
		if (uploadQueue.length > 0) {
			setSaveChanges(true);
			const uploadCurrentVideo = async () => {
				const video = uploadQueue[0];

				try {
					const { data: uploadedVideo, error: uploadVideoError } = await apiRetryHandler(
						async () => await uploadVideoFile(video)
					);
					setUploadQueue((prev) => prev.slice(1));

					if (uploadVideoError) {
						const errorText = getErrorString(translation, uploadVideoError?.response?.data?.error);
						setEditVideoError(errorText);
						return;
					}
					setIgProcessingList((prev) => [...prev, uploadedVideo]);
				} catch (error: any) {
					const errorText = getErrorString(translation, error?.response?.data?.error);
					setEditVideoError(errorText);
				}
			};

			uploadCurrentVideo();
		} else {
			setSaveChanges(false);
		}
	}, [uploadQueue]);

	const monitorVideoJobs = async () => {
		// Prevent re-entry if already monitoring
		if (isMonitoring.current) return;
		isMonitoring.current = true;

		for (const job of igProcessingList) {
			await checkJobStatusSequentially(job);
		}

		isMonitoring.current = false;
	};

	const checkJobStatusSequentially = async (initialJobStatus: VideoEncodeResponse) => {
		let currentJobStatus = initialJobStatus;
		while (currentJobStatus.status !== JobVideoStatus.COMPLETE) {
			try {
				if (currentJobStatus.status === JobVideoStatus.FAILED) {
					setIgProcessingList((prevJobs) =>
						prevJobs.filter((jobItem) => jobItem.tempFilename !== currentJobStatus.tempFilename)
					);
					throw new Error(`Video processing failed: ${currentJobStatus.statusMessage}`);
				}

				const { data: jobStatus } = await apiRetryHandler(
					async () => await getVideoJobStatus(currentJobStatus.tempFilename)
				);

				if (jobStatus.status === JobVideoStatus.COMPLETE) {
					const { data: videoData } = await apiRetryHandler(async () => await getVideoFile(jobStatus.videoId));

					if (videoData.video) {
						setVideos((prevVideos) => [videoData.video, ...prevVideos]);
						setIgProcessingList((prevJobs) =>
							prevJobs.filter((jobItem) => jobItem.tempFilename !== currentJobStatus.tempFilename)
						);
					}
					break;
				} else {
					currentJobStatus = jobStatus;
					await waitForNextStatusCheck(currentJobStatus.nextStatusCheck);
				}
			} catch (error: unknown) {
				console.error("Error while checking job status:", (error as Error).message);
				break;
			}
		}
	};

	useEffect(() => {
		if (igProcessingList.length > 0) monitorVideoJobs();
	}, [igProcessingList]);

	const handleVideoDelete = async () => {
		try {
			const { error } = await apiRetryHandler(async () => await deleteVideoFile(deleteVideoId));

			if (error) {
				const errorText = getErrorString(translation, error?.response?.data?.error);
				setEditVideoError(errorText);
				setShowDeletionModal(false);
				setDeleteVideoId("");
				return;
			}
			setVideos(videos.filter((video) => video._id !== deleteVideoId));
			setShowDeletionModal(false);
			setDeleteVideoId("");
		} catch (error: any) {
			const errorText = getErrorString(translation, error?.response?.data?.error);
			setEditVideoError(errorText);
			setShowDeletionModal(false);
			setDeleteVideoId("");
		}
	};

	useEffect(() => {
		// Prevent navigation if changes are not saved
		if (saveChanges) window.history.pushState(null, "", window.location.pathname);

		// prompt before refresh page
		const handleBeforeUnload = (event: {preventDefault: () => void; returnValue: string}) => {
			if (saveChanges) {
				event.preventDefault();
				event.returnValue = "";
			}
		};

		const handlePopState = () => {
			if (saveChanges) {
				window.history.pushState(null, "", window.location.pathname);
				setShowConfirmLeavingModal(true);
				setGoToPrevPage(true);
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);
		window.addEventListener("popstate", handlePopState);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
			window.removeEventListener("popstate", handlePopState);
		};
	}, [uploadQueue, setShowConfirmLeavingModal, saveChanges]);

	return (
		<>
			<ConfirmationBoxWrapper>
				{!!editVideoError && (
					<ErrorMessage error={editVideoError} setError={setEditVideoError} displayCloseIcon={true} />
				)}
			</ConfirmationBoxWrapper>
			<PageBody>
				<HeadingText>{translation.videoLibraryPage.pageTitle}</HeadingText> &nbsp;
				<SubheadingText>{translation.videoLibraryPage.pageSubtitle}</SubheadingText> &nbsp;
				{!hideCreateVideo && (
					<PageSection style={{ display: "flex", gap: "1rem" }}>
						<PageSubsection style={{ margin: 0 }}>
							<BulkUploadContainer
								style={{ cursor: "pointer" }}
								{...getVideoProps()}
								data-testid="fileDropDiv"
							>
								<div style={{ textAlign: "center" }}>
									<b>{translation.videoLibraryPage.uploadVideosText}</b>
									<br />
									<small>{translation.videoLibraryPage.uploadVideosSubtext}</small>
								</div>
								<MainButton>{translation.general.browse}</MainButton>
							</BulkUploadContainer>
						</PageSubsection>
					</PageSection>
				)}
				<VideoGrid>
					{uploadQueue.map((video, index) => (
						<FlexCol key={`upload-${video.name}-${index}`}>
							<VideoTile>
								<FlexCol style={{ textAlign: "center" }}>
									<Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "50px", margin: "auto" }} />
									{translation.videoLibraryPage.uploadingStatus}
								</FlexCol>
							</VideoTile>
						</FlexCol>
					))}
					{igProcessingList.map((video) => (
						<FlexCol key={video.tempFilename}>
							<VideoTile>
								<FlexCol style={{ textAlign: "center" }}>
									<Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "50px", margin: "auto" }} />
									{translation.videoLibraryPage.processingStatus}
								</FlexCol>
							</VideoTile>
						</FlexCol>
					))}
					{videos.map((video) => {
						const formattedVideoName = formatVideoName(video.videoFileLocation);
						return video?.publicPosterURL ? (
							<FlexCol key={video._id}>
								<VideoTile
									onClick={() => {
										setVideoPreviewUrl(video.publicVideoURL);
										setShowVideoPreviewModal(true);
									}}
								>
									<VideoPoster src={video.publicPosterURL} alt="Video Poster" />
									<TrashIcon
										className="icon-hover"
										onClick={(e) => {
											e.stopPropagation();
											setDeleteVideoId(video._id);
											setShowDeletionModal(true);
										}}
									/>
								</VideoTile>
								<VideoInfo title={formattedVideoName}>{formattedVideoName}</VideoInfo>
								<VideoUploadTime>{timeAgo(video.createdAt, translation)}</VideoUploadTime>
							</FlexCol>
						) : (
							<FlexCol key={video.tempFilename}>
								<VideoTile>
									<div>
										<WarningIcon src={CorruptFileIcon} />
										{translation.errors.fileCorrupt}
									</div>
									<TrashIcon
										className="icon-hover"
										onClick={(e) => {
											e.stopPropagation();
											setDeleteVideoId(video._id);
											setShowDeletionModal(true);
										}}
									/>
								</VideoTile>
								<VideoInfo>{translation.errors.fileCorrupt}</VideoInfo>
								<VideoUploadTime>{timeAgo(video.createdAt, translation)}</VideoUploadTime>
							</FlexCol>
						);
					}
					)}
				</VideoGrid>
				{showMoreButtonVisible && (
					<MainButton style={{ margin: "auto", display: "block", marginTop: "2rem" }} onClick={fetchVideos}>
						{translation.general.showMore}
					</MainButton>
				)}
			</PageBody>
			<VideoPreviewModal
				visible={showVideoPreviewModal}
				videoUrl={videoPreviewUrl}
				onClose={() => {
					setShowVideoPreviewModal(false);
					setVideoPreviewUrl("");
				}}
			/>
			<ConfirmVideoDeleteModal
				visible={showDeletionModal}
				onCancel={() => {
					setShowDeletionModal(false);
					setDeleteVideoId("");
				}}
				onContinue={handleVideoDelete}
			/>
			<ConfirmLeavingModal
				visible={showConfirmLeavingModal}
				onCancel={() => {
					setShowConfirmLeavingModal(false);
					setGoToPrevPage(false);
				}}
				onContinue={() => {
					if (goToPrevPage) window.history.go(-2);
					else navigate(navigationUrl);
				}}
			/>
		</>
	);
};

export default VideoLibrary;
