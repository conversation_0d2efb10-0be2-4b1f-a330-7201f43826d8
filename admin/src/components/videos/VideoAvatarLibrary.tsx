/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { ConfirmationBoxWrapper, HeadingText, SubheadingText } from "@src/styles/forms";
import { useTranslation } from "../hooks/translations";
import { useTokenCheck } from "../hooks/useTokenCheck";
import {
    PageBody,
    PageSection,
    MainButton,
    VideoGrid,
    VideoTile,
    VideoInfo,
    VideoPoster,
    FlexCol
} from "@src/styles/components";
import { getErrorString } from "../hooks/getErrorString";
import ErrorMessage from "@src/components/utils/ErrorMessage";
import Lottie from "lottie-react";
import { LoadingSpinnerAnimation } from "@src/assets/lottie_animations/loading-spinner";
import { HeyGenAvatar, HeyGenTalkingPhoto } from "@src/types/heygen";
import getHeyGenAvatars from "../hooks/getHeyGenAvatars";
import generateHeyGenVideo from "../hooks/generateHeyGenVideo";
import getHeyGenVideoStatus from "../hooks/getHeyGenVideoStatus";
import styled from "styled-components";

interface Props {
    saveChanges: boolean;
    setSaveChanges: (value: boolean) => void;
    navigationUrl: string;
    showConfirmLeavingModal: boolean;
    setShowConfirmLeavingModal: (value: boolean) => void;
    hideCreateVideo: boolean;
}

const CreateAvatarSection = styled.div`
    background: ${(props) => props.theme.colors.videoSectionBackground};
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    margin: 2rem 0;
`;

const CreateAvatarTitle = styled.h2`
    font-family: ${(props) => props.theme.fonts.family};
    color: ${(props) => props.theme.colors.apFormText};
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
`;

const CreateAvatarDescription = styled.p`
    font-family: ${(props) => props.theme.fonts.family};
    color: ${(props) => props.theme.colors.apFormText};
    font-size: 1rem;
    margin-bottom: 2rem;
    line-height: 1.5;
`;

const SectionTitle = styled.h3`
    font-family: ${(props) => props.theme.fonts.family};
    color: ${(props) => props.theme.colors.apFormText};
    font-size: 1.25rem;
    font-weight: 600;
    margin: 2rem 0 1rem 0;
`;

const SectionDescription = styled.p`
    font-family: ${(props) => props.theme.fonts.family};
    color: ${(props) => props.theme.colors.apThirdButtonActive};
    font-size: 0.9rem;
    margin-bottom: 1rem;
`;

const CarouselContainer = styled.div`
    position: relative;
    margin: 2rem 0;
`;

const CarouselNavigation = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
`;

const NavButton = styled.button`
    background: ${(props) => props.theme.colors.apButton};
    color: ${(props) => props.theme.colors.apWhite};
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    
    &:hover {
        background: ${(props) => props.theme.colors.apButtonHover};
    }
    
    &:disabled {
        background: ${(props) => props.theme.colors.apThirdButtonActive};
        cursor: not-allowed;
    }
`;

const AvatarGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
    overflow: hidden;
`;

const AvatarTile = styled.div<{ selected?: boolean }>`
    background-color: ${(props) => props.theme.colors.apWhite};
    border: 2px solid ${(props) => props.selected ? props.theme.colors.apButton : "transparent"};
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    
    &:hover {
        border-color: ${(props) => props.theme.colors.apButton};
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
`;

const AvatarImage = styled.img`
    width: 100%;
    height: 120px;
    object-fit: cover;
`;

const AvatarName = styled.div`
    padding: 0.75rem;
    font-family: ${(props) => props.theme.fonts.family};
    color: ${(props) => props.theme.colors.apFormText};
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
`;

const UseAvatarButton = styled.button`
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: ${(props) => props.theme.colors.apButton};
    color: ${(props) => props.theme.colors.apWhite};
    border: none;
    padding: 0.5rem;
    font-family: ${(props) => props.theme.fonts.family};
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transform: translateY(100%);
    transition: transform 0.2s ease;
    
    ${AvatarTile}:hover & {
        transform: translateY(0);
    }
`;

const GeneratingVideoSection = styled.div`
    background: ${(props) => props.theme.colors.videoSectionBackground};
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    margin: 2rem 0;
`;

const VideoAvatarLibrary: React.FC<Props> = ({
    navigationUrl,
    saveChanges,
    setSaveChanges,
    showConfirmLeavingModal,
    setShowConfirmLeavingModal,
    hideCreateVideo
}) => {
    const translation = useTranslation();
    const { apiRetryHandler } = useTokenCheck();
    
    const [avatars, setAvatars] = useState<HeyGenAvatar[]>([]);
    const [talkingPhotos, setTalkingPhotos] = useState<HeyGenTalkingPhoto[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");
    const [selectedAvatar, setSelectedAvatar] = useState<HeyGenAvatar | null>(null);
    const [generatingVideo, setGeneratingVideo] = useState(false);
    const [generatedVideoId, setGeneratedVideoId] = useState<string | null>(null);
    const [currentPage, setCurrentPage] = useState(0);
    
    const avatarsPerPage = 6;
    const totalPages = Math.ceil(avatars.length / avatarsPerPage);
    const currentAvatars = avatars.slice(currentPage * avatarsPerPage, (currentPage + 1) * avatarsPerPage);

    useEffect(() => {
        fetchAvatars();
    }, []);

    const fetchAvatars = async () => {
        setLoading(true);
        try {
            const { data, error: fetchError } = await getHeyGenAvatars();
            
					 
						console.log("Full HeyGen response:", data);
						console.log("Avatars:", data?.avatars);					
						console.log("Data type:", typeof data);

            if (fetchError) {
                const errorText = getErrorString(translation, fetchError?.response?.data?.error);
                setError(errorText);
            } else if (data) {
                setAvatars(data.avatars || []);
								console.log(data.avatars);
                setTalkingPhotos(data.talking_photos || []);
            }
        } catch (err) {
            setError("Failed to load avatars");
        } finally {
            setLoading(false);
        }
    };

    const handleAvatarSelect = async (avatar: HeyGenAvatar) => {
        setSelectedAvatar(avatar);
        setGeneratingVideo(true);
        setSaveChanges(true);

        try {
            const videoRequest = {
                title: `Avatar Video - ${avatar.avatar_name}`,
                video_inputs: [
                    {
                        character: {
                            type: "avatar" as const,
                            avatar_id: avatar.avatar_id,
                            scale: 1.0,
                            avatar_style: "normal" as const
                        },
                        voice: {
                            type: "text" as const,
                            voice_id: "default", // You'll need to get available voices
                            input_text: "Hello! This is a sample avatar video generated using HeyGen API.",
                            speed: 1.0
                        },
                        background: {
                            type: "color" as const,
                            value: "#f6f6fc"
                        }
                    }
                ],
                dimension: {
                    width: 1080,
                    height: 1920
                },
                caption: false
            };

            const { data: videoData, error: videoError } = await generateHeyGenVideo(videoRequest);
            
            if (videoError) {
                const errorText = getErrorString(translation, videoError?.response?.data?.error);
                setError(errorText);
            } else if (videoData) {
                setGeneratedVideoId(videoData.video_id);
                // Start polling for video status
                pollVideoStatus(videoData.video_id);
            }
        } catch (err) {
            setError("Failed to generate avatar video");
        } finally {
            setGeneratingVideo(false);
            setSaveChanges(false);
        }
    };

    const pollVideoStatus = async (videoId: string) => {
        const checkStatus = async () => {
            const { data: statusData, error: statusError } = await getHeyGenVideoStatus(videoId);
            
            if (statusError) {
                console.error("Error checking video status:", statusError);
                return;
            }
            
            if (statusData) {
                if (statusData.status === "completed" && statusData.video_url) {
                    // Video is ready, you can handle the completed video here
                    console.log("Video completed:", statusData.video_url);
                } else if (statusData.status === "failed") {
                    setError("Video generation failed");
                } else if (statusData.status === "processing" || statusData.status === "pending") {
                    // Continue polling
                    setTimeout(checkStatus, 5000);
                }
            }
        };
        
        checkStatus();
    };

    const handlePrevPage = () => {
        setCurrentPage(Math.max(0, currentPage - 1));
    };

    const handleNextPage = () => {
        setCurrentPage(Math.min(totalPages - 1, currentPage + 1));
    };

    if (loading) {
        return (
            <PageBody>
                <div style={{ textAlign: "center", padding: "4rem" }}>
                    <Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "100px", margin: "auto" }} />
                    <p>Loading avatars...</p>
                </div>
            </PageBody>
        );
    }

    return (
        <>
            <ConfirmationBoxWrapper>
                {!!error && (
                    <ErrorMessage error={error} setError={setError} displayCloseIcon={true} />
                )}
            </ConfirmationBoxWrapper>
            
            <PageBody>
                <HeadingText>Video Avatars</HeadingText>
                <SubheadingText>Create and edit your custom or public avatars.</SubheadingText>

                {!hideCreateVideo && (
                    <CreateAvatarSection>
                        <CreateAvatarTitle>Create Your Avatar</CreateAvatarTitle>
                        <CreateAvatarDescription>
                            Record a 2 minute video and bring your avatar to<br />
                            life so you can create fresh content without the<br />
                            hassle of filming and editing.
                        </CreateAvatarDescription>
                        <MainButton>Get Started</MainButton>
                    </CreateAvatarSection>
                )}

                <SectionTitle>Public Avatars</SectionTitle>
                <SectionDescription>Select an avatar from our library to use for your videos.</SectionDescription>

                {generatingVideo && (
                    <GeneratingVideoSection>
                        <Lottie animationData={LoadingSpinnerAnimation} loop={true} style={{ width: "60px", margin: "auto" }} />
                        <p>Generating avatar video...</p>
                    </GeneratingVideoSection>
                )}

                <CarouselContainer>
                    <CarouselNavigation>
                        <NavButton onClick={handlePrevPage} disabled={currentPage === 0}>
                            ←
                        </NavButton>
                        <NavButton onClick={handleNextPage} disabled={currentPage >= totalPages - 1}>
                            →
                        </NavButton>
                    </CarouselNavigation>

                    <AvatarGrid>
                        {currentAvatars.map((avatar) => (
						
                            <AvatarTile 
                                key={avatar.avatar_id} 
                                selected={selectedAvatar?.avatar_id === avatar.avatar_id}
                            >
                                <AvatarImage 
                                    src={avatar.preview_image_url} 
                                    alt={avatar.avatar_name}
                                    onError={(e) => {
                                        // Fallback to a placeholder if image fails to load
                                        (e.target as HTMLImageElement).src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkF2YXRhcjwvdGV4dD48L3N2Zz4=";
                                    }}
                                />
                                <AvatarName>{avatar.avatar_name}</AvatarName>
                                <UseAvatarButton onClick={() => handleAvatarSelect(avatar)}>
                                    Use Avatar
                                </UseAvatarButton>
                            </AvatarTile>
                        ))}
                    </AvatarGrid>
                </CarouselContainer>
            </PageBody>
        </>
    );
};

export default VideoAvatarLibrary;
