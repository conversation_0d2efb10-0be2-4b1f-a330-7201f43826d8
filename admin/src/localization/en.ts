export default {
	locale: "en_US",
	general: {
		dashboard: "Dashboard",
		apRetailText: "Retail™",
		profile: "Profile",
		performance: "Performance",
		configuration: "Configuration",
		collections: "Collections",
		home: "Home",
		videoLibrary: "Video Library",
		library: "Library",
		videos: "Videos",
		accountSettings: "Settings",
		freePlan: "Basic",
		pro: "Pro",
		free: "Free",
		basic: "Basic",
		proPlan: "Pro",
		enterprise: "Enterprise",
		unlockPro: "Unlock Pro",
		downgrade: "Downgrade",
		upgrade: "Upgrade",
		select: "Select",
		share: "Share",
		trialDays: "Trial Days",
		unlockProPlan: "Unlock Pro",
		cancelProPlan: "Cancel Subscription",
		changeSubscription: "Change Subscription",
		firstName: "First Name",
		lastName: "Last Name",
		companyName: "Company Name",
		billingContact: "Billing Contact",
		addressLine1: "Address Line 1",
		addressLine2: "Address Line 2",
		city: "City/Town",
		province: "Province/State",
		postalCode: "Zip Code/Postal Code",
		country: "Country",
		email: "Email",
		password: "Password",
		signIn: "Sign In",
		signInEmail: "Sign In with Email",
		signInIntuit: "Sign In with Intuit",
		signOut: "Sign Out",
		createAccount: "Create Account",
		signUpForFree: "Sign Up for Free!",
		checkInbox: "Check Your Inbox",
		forgotPassword: "I forgot my password",
		learnMore: "Learn More",
		accessMore: "Access more",
		features: "features",
		and: "and",
		metrics: "metrics",
		with: "with",
		continue: "Continue",
		goBack: "Go Back",
		error: "Error",
		create: "Create",
		cancel: "Cancel",
		refresh: "Refresh",
		confirm: "Confirm",
		update: "Update",
		upload: "Upload",
		replace: "Replace",
		remove: "Remove",
		done: "Done",
		save: "Save",
		browse: "Browse",
		clickHere: "Click here",
		generate: "Generate",
		closeWindow: "Close Window",
		showMore: "Show More",
		delete: "Delete",
		close: "Close",
		connect: "Connect",
		copyright: "© {0}",
		tosText: "Terms of Service",
		tosLink: process.env.TERMS_OF_SERVICE_URL_EN,
		privacyPolicyText: "Privacy Policy",
		privacyPolicyLink: process.env.PRIVACY_POLICY_URL_EN,
		title: "Title",
		help: "Help",
		settings: "Settings",
		helpEmail: process.env.HELP_EMAIL_EN,
		emailSubject: "I need help",
		invitePending: "Invite Pending",
		addToSite: "Add to Site",
		inputCharRemainingText: "Characters",
		yearsAgo: "years ago",
		monthsAgo: "months ago",
		weeksAgo: "weeks ago",
		daysAgo: "days ago",
		hoursAgo: "hours ago",
		minutesAgo: "minutes ago",
		secondsAgo: "seconds ago",
		yearAgo: "year ago",
		monthAgo: "month ago",
		weekAgo: "week ago",
		dayAgo: "day ago",
		hourAgo: "hour ago",
		minuteAgo: "minute ago",
		secondAgo: "second ago",
		justNow: "just now",
		unlockapPro: "Unlock Pro",
		unlock: "Unlock",
		replaceVideo: "Replace Video",
		editCaptions: "Edit Captions",
		avatars: "Avatars"
	},
	errors: {
		passwordFormat: "Password must be at least 8 characters.",
		emailFormat: "Please use a valid email address.",
		phoneFormat: "Please use a valid phone number.",
		tokenError: "There was an issue verifying your account. Please try again.",
		unexpectedError: "Sorry, we've run into an issue. Please refresh and try again.",
		internalError: "Sorry, we've run into an issue. Please refresh and try again.",
		missingEventName: "The event name was missing.",
		missingEventData: "The event data is missing.",
		missingAccountId: "There was an issue saving the changes to your account. Please try again.",
		passwordComplexity: "Password must be at least 8 characters.",
		invalidToken: "There was an issue verifying your account. Please try again.",
		accountExists: "Sorry, there is already an account set up with this email.",
		alreadyVerified: "You've already verified your email. Please sign in.",
		hashFailure: "Sorry, we've run into an issue. Please refresh and try again.",
		serviceFailed: "Sorry, we've run into an issue. Please refresh and try again.",
		invalidAccount: "There was an issue with your request. Please try again.",
		userNotVerified: "Please verify your account. Check your email for the verification email to continue.",
		databaseFailure: "Sorry, we've run into an issue. Please refresh and try again.",
		invalidMethod: "There was an issue signing in. Please try again.",
		notAuthenticated: "There was an issue verifying your account. Please try again.",
		emailNotFound: "Sorry, we can't find an account associated with this email.",
		missingKey: "There was an issue resetting your password. Please try again.",
		missingId: "There was an issue resetting your password. Please try again.",
		emailDelivery: "There was an issue sending the verification email. Please try again.",
		missingToken: "Sorry, we've run into an issue. Please sign in and try again.",
		tokenVerification: "There was an issue verifying your account. Please try again.",
		missingHashkey: "Sorry, we've run into an issue. Please sign in and try again.",
		accessForbidden: "You don't have permission to view this page.",
		collectionNotFound: "There was an issue loading your videos. Please try again.",
		fileWriteFailure: "There was an issue generating your video. Please try again.",
		invalidInput: "The fields you've entered are invalid. Please try again.",
		legalRequiredError: "You must agree before submitting.",
		videoSize: "Please use a supported file type that is less than 512MB.",
		imageFormat: "Please use a JPG or PNG file.",
		canvas2D: "Canvas 2D context is not supported.",
		optimizeImageError: "Failed to optimize image. Please try again.",
		videoInUse: "Video is currently in use and cannot be deleted.",
		igBadRequest: "Requested permissions not provided.",
		signInEmailError:
			"Sorry, this email is not associated with an account. Please Create an Account or check the email and try again.",
		signUpExistingEmail: "An account with this email address already exists.",
		signUpInvalidFormat: "Please use a valid email address to continue.",
		signUpGeneric: "There was an issue creating your account. Please try again.",
		emailSignInNoEmail: "We can't find an account connected to this email. Please try again or Sign Up.",
		emailSignInInvalidFormat: "Please use a valid email address to continue.",
		emailSignInGeneric: "There was an issue signing in. Please try again.",
		signInIncorrect: "The email or password provided was incorrect. Please try again.",
		emailSignInvalidFormat: "Please use a valid email address to continue.",
		signInGeneric: "There was an issue signing in. Please try again.",
		forgotPasswordNoEmail: "There is no account connected to the email provided. Please check the email and try again.",
		forgotPasswordGeneric: "There was an issue sending you the password reset email. Please try again.",
		videoLibraryFileTooLarge: "Please use files less than 512mb.",
		videoLibraryWrongFileType: "Please use an MP4, MOV, WMV, or AVI file.",
		videoLibraryVideoNotProcessed: "There was an issue processing your video. Please check the file and try again.",
		videoLibraryCantLoadVideos: "There was an issue loading your videos. Please refresh the page.",
		videoLibraryGeneric: "We ran into an issue. Please refresh the page.",
		videoManagerCantLoadVideos: "There was an issue loading your videos. Please refresh the page.",
		videoManagerCantReorder: "There was an issue reordering your videos. Please refresh the page and try again.",
		videoManagerGeneric: "We ran into an issue. Please refresh the page.",
		videoDetailNoTitle: "Please add a title to your video to save.",
		videoDetailNoVideo: "Please add a video asset to save.",
		videoDetailNoTitleLink: "Please add a title for your link to save.",
		videoDetailNoLinkImage: "Please add an image to your link to save.",
		videoDetailFileSize: "Please use files less than 512mb.",
		videoDetailWrongFormat: "Please use an MP4, MOV, WMV, or AVI file.",
		videoDetailCantProcess: "There was an issue processing your video. Please check the file and try again.",
		videoDetailCantSave: "There was an issue saving your video. Please refresh the page and try again.",
		videoDetailGeneric: "We ran into an issue. Please refresh the page.",
		collectionsCantLoad: "There was an issue loading your collections. Please refresh the page.",
		collectionsCantDelete: "There was an issue deleting your collection. Please refresh the page and try again.",
		collectionsGeneric: "We ran into an issue. Please refresh the page and try again.",
		collectionDetailCantLoad: "There was an issue loading your videos. Please refresh the page.",
		collectionDetailCantReorder: "There was an issue reordering your videos. Please refresh the page and try again.",
		collectionDetailCantSave: "There was an issue saving your changes. Please refresh the page and try again.",
		collectionDetailCantAddVideos:
			"There was an issue adding videos to this collection. Please refresh the page and try again.",
		collectionDetailGeneric: "We ran into an issue. Please refresh the page.",
		profileCantLoad: "There was an issue loading your profile details. Please refresh the page.",
		profileCantSave: "There was an issue saving your changes. Please refresh the page and try again.",
		profileGeneric: "We ran into an issue. Please refresh the page.",
		profileCantSendReset: "There was an issue sending a password reset email. Please refresh the page and try again.",
		settingsCantLoad: "There was an issue loading your details. Please refresh the page.",
		settingsCantSave: "There was an issue saving your changes. Please refresh the page and try again.",
		settingsGeneric: "We ran into an issue. Please refresh the page.",
		settingsCantInviteUser: "There was an issue sending an invite. Please refresh the page and try again.",
		settingsInvalidInviteEmail: "Please use a valid email address.",
		settingsCantDeleteUser: "There was an issue removing this user. Please refresh the page and try again.",
		settingsWrongFileType: "Please use a JPG or PNG less than 2mb.",
		performanceCantLoad: "There was an issue loading your performance metrics. Please refresh the page.",
		performanceCantRefresh:
			"There was an issue refreshing your performance metrics. Please refresh the page and try again.",
		performanceNoAvailableData: "There is no data available for this date range.",
		performanceTimeout: "We ran into an issue. Please refresh the page and try again.",
		performanceGeneric: "We ran into an issue. Please refresh the page.",
		generalErrorCantLoad: "There was an issue loading the page. Please refresh.",
		generalErrorGeneric: "We ran into an issue. Please refresh the page.",
		generalErrorSignedOut: "You've been signed out. Please sign in to view this page.",
		authMethodNotSupported: "This action can't be completed with your account.",
		authDuplication: "An account with this information already exists.",
		instagramUrlError: "To add videos from Instagram please download the video file and upload.",
		youTubeUrlError:
			"This method will not work for YouTube videos. Download the video asset first before attempting to upload.",
		vimeoUrlError:
			"This method will not work for Vimeo videos. Download the video asset first before attempting to upload.",
		tiktokUrlError:
			"This method will not work for Tiktok videos. Download the video asset first before attempting to upload.",
		videoUrlError:
			"This link will not work. Please download the video asset first before attempting to upload.",
		userLimitReached: "You've reached the maximum number of users for your account.",
		emailAlreadyExists: "This email is already in use.",
		emailAlreadyInvited: "This email has already been invited.",
		fileCorrupt: "File Corrupt",
		addVideoTitle: "Please add a title to your video.",
		uploadVideo: "Please upload a video to continue.",
		addLinkTitle: "Please add a title for all links you've included."
	},
	createAccountPage: {
		subText: "Create your account to start creating Interactive Videos for your website - it's FREE!",
		question: "Already have an account?",
		businessEmail: "Business Email",
		email: "Email",
		verifyEmail: "Verify Email",
		emailConfirmed: "Email Confirmed!",
		verifyEmailSubText: "You're ready to start creating and converting!",
		checkInbox:
			"Please check your inbox for your email verification in order to continue. If you didn't receive an email, check your junk and spam folders.",
		legalCheckbox: "By creating an account you agree to the {0}Terms of Service{1} and {2}Privacy Policy{3}",
		createAccTitle: "Create a free account and start using Interactive Videos!",
		createAccCopy:
			"Interactive Videos let you leverage reviews, user-generated content, social stories, and your own content, and use it directly on your website. And it's free to use forever!",
		verifyEmailTitle: "You’re so close!",
		verifyEmailCopy: "Once you’ve verified your email - you’re ready to start.",
		emailConfirmedTitle: "Welcome!",
		emailConfirmedCopy:
			"Sell more online, more often, with video shopping. Accelerate conversions by providing interactive content, personalized support, and product recommendations.",
		verifyEmailSignUpCopy: "Check your email - we've sent you a link to sign in.",
		verifySignInTitle: "Welcome back!",
		verifySignInCopy: "Click Continue to go to your Dashboard."
	},
	AddCompanyPage: {
		pageTitle: "Create company",
		addCompany: "Create company",
		subText: "Create your company to start creating videos on your website.",
		companyName: "Company Name",
		createCompany: "Create Company",
		skip: "Skip",
		imgTitle: "Create a free account!",
		imgCopy:
			"Videos let you leverage reviews, user-generated content, social stories, and your own content, and use it directly on your website. And it's free to sign up!"
	},
	companyPage: {
		pageTitle: "Company",
		welcome: "Hi",
		selectCompany: "Please select a company to sign in with.",
		addCompany: "+ Add New Company",
		loadingIssue: "⚠️ There was an issue loading your account. Please refresh and try again.",
		selectedCharGroup: "A",
		alphabet: "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
		numbers: "123",
		specialChars: "!?$"
	},
	accountSettingsPage: {
		pageTitle: "Settings",
		generalTabHeading: "General",
		installationTabHeading: "Installation",
		billingTabHeading: "Billing + Plan",
		shopifyConversion: "Shopify Conversion Settings",
		needHelp: "Need help?",
		resourcesText: "We've got a bunch of resources to help you each step of the way.",
		trackConversions1:
			"To access Additional scripts, start from your Shopify admin, and go to Settings > Checkout. Under Order status page you find the Additional scripts section.",
		trackConversions2: "Copy and paste the following code in your Additional scripts section:",
		installationGuide: "Installation Guide",
		resources: "Resources",
		plan: "Plan",
		billingDetails: "Billing Details",
		billingAddress: "Billing Address",
		enterPaymentDetails: "Enter Payment Details",
		paymentMethod: "Payment Method",
		paymentMethodLink: "Add a payment method when you",
		paymentMethodChange: "Change Payment Method",
		paymentMethodText: "upgrade to Pro",
		invoices: "Invoices",
		invoicesText: "You don't have any invoices.",
		invoiceCompanyName: "This will appear on your invoice.",
		invoiceEmail: "All billing-related emails will be sent here.",
		usersTitle: "Users",
		saveChanges: "Save Changes",
		inviteUser: "Invite User",
		companyDetails: "Company Details",
		companyLogo: "Company Logo",
		scriptStatus: "Core Script Status",
		uploadText: "Drag and drop logo or upload manually.",
		upload: "Upload",
		company: "Company",
		companyURL: "Company URL",
		loadingIssue: "⚠️ There was an issue loading your account. Please refresh and try again.",
		dataUpdated: "Updated successfully!",
		scriptDetected: "Your Core Script is successfully added to your website. Need to make changes?",
		noScriptDetected: "We can't detect the Core Script on your website. Please verify it's been installed properly.",
		clickHere: "Click here to get your Core Script.",
		usersName: "Name",
		usersEmail: "Email",
		usersStatus: "Status",
		apiKeys: "API Keys",
		generateKey: "Generate Key",
		subscriptionText1: "Your paid subscription will start in ",
		subscriptionText2: "days",
		subscriptionText3: "after your trial is over.",
		trialExpiryText1: "Your Pro Plan trial",
		trialExpiryText2: "expires in",
		trialExpiryText3: "days.",
		trialExpiryText4: "Your Paid Subscription will end in",
		trialExpiryText5: "and you will return to the Basic Plan",
		addPaymentMethod: "Add Payment Method"
	},
	appCustomization: {
		displayPreferences: "Display Preferences",
		generalTab: "General",
		carouselTab: "Carousel",
		widgetTab: "Widget",
		inlineTab: "Inline",
		buttonBackgroundColor: "Button Background Color",
		buttonBackgroundBlur: "Button Background Blur",
		iconTextColor: "Icon + Text Color",
		borderRadius: "Border Radius",
		centerOnPage: "Center on Page",
		MarginLeftRight: "Margin (Left + Right)",
		paddingBetween: "Padding (Between Videos)",
		position: "Position",
		leftCorner: "Left Corner",
		rightCorner: "Right Corner",
		font: "Font",
		closedCaptionsNotAvailable: "Closed captions are not available for this video."
	},
	editCollection: {
		untitledCollection: "Untitled Collection",
		placeholder: "Add a Title",
		addVideo: "Add a video to this collection",
		addVideos: "Add more videos to this collection",
		videos: "Videos",
		createVideo: "Create New Video",
		selectVideos: "Select Videos",
		pageTitle: "Edit Collection",
		saveChanges: "Save Changes",
		loadingIssue: "⚠️ There was an issue loading your account. Please refresh and try again.",
		dataUpdated: "Updated successfully!",
		collectionId: "Collection ID"
	},
	createCollectionPage: {
		untitledCollection: "Untitled Collection",
		placeholder: "Add a Title",
		addVideo: "Add a video to this collection",
		addVideos: "Add more videos to this collection",
		videos: "Videos",
		createVideo: "Create New Video",
		selectVideos: "Select Videos",
		pageTitle: "Create Collection",
		saveChanges: "Save Changes",
		loadingIssue: "⚠️ There was an issue loading your account. Please refresh and try again.",
		dataUpdated: "Updated successfully!"
	},
	addAccountPage: {
		pageTitle: "Add account",
		saveChanges: "Save Changes",
		createUser: "Create a new user",
		accountCreated: "Account created successfully!"
	},
	profilePage: {
		pageTitle: "Profile",
		saveChanges: "Save Changes",
		yourCompanies: "Your Companies",
		personalDetails: "Personal Details",
		email: "Email",
		firstName: "First name",
		lastName: "Last name",
		loadingIssue: "⚠️ There was an issue loading your account. Please refresh and try again.",
		dataUpdated: "Updated successfully!",
		password: "Password",
		noPasswordSetText:
			"You haven't set a password. You can set one by clicking the following link. It will send you an email to create a new password:",
		setPasswordText: "Set a Password",
		forgotPasswordText:
			"Forget your password? You can reset it by clicking the following link. We'll send you an email to create a new password.",
		resetPasswordText: "Reset my Password"
	},
	createVideoPage: {
		pageTitle: "Create Video",
		shoppableVideo: "Create Video",
		generate: "Save",
		videoTitle: "Video Title",
		subTitle: "Subtitle",
		videoDescription: "Video Description",
		videoDescPlaceholder: "Enter a description for your video.",
		enterTitle: "Enter Title",
		buttonText: "Button Text",
		coverImageText: "Cover Image",
		enterButtonText: "Enter Button Text",
		videoButtonText: "Shop Now",
		videoSnippet: "Video Snippet",
		product: "Link",
		products: "Featured Links",
		productURL: "URL",
		phoneNumber: "Phone Number",
		emailAddress: "Email",
		addInteraction: "Add an Interaction",
		productsText: "Add product or page links below to feature them in your video.",
		walkthroughText: "Click here for walkthroughs on adding it to different website platforms.",
		walkthroughLink: process.env.WALKTHROUGH_URL_EN,
		uploadVideoText: "Add a Video",
		uploadingStatus: "Uploading",
		encodingPrepStatus1: "Hang tight!\nPreparing to optimize your video…",
		encodingPrepStatus2: "Starting up the optimize engine",
		encodingPrepStatus3: "Almost there!",
		encodingStatus: "Optimizing",
		videoSize: "1080px x 1920px",
		maxFileSize: "Maximum File Size 512MB",
		recommendedSize: "Recommended Size:",
		uploadVideoTextLine1: "You can upload a video file or",
		uploadVideoTextLine2: "provide a link to public URL",
		videoFormat: "MP4, MOV, AVI, or WMV Format",
		addImage: "Add Image",
		replaceCoverImage: "Cover Image",
		uploadCoverImage: "Cover Image",
		generatingCoverImage: "Generating Cover Image...",
		fetchingProductText: "Collecting link details…",
		uploadImageAltText: "Upload Image",
		collectionsSubheading: "Collections",
		addToCollectionText: "Add to Collection...",
		allVideosText: "All Videos",
		videosText: "Videos",
		displayTitle: "Display Title",
		displayCaptions: "Display Captions",
		performanceSubheading: "Performance",
		performanceNoData: "No performance data yet.",
		createCollectionText: "+ Create Collection",
		newCollectionText: "New Collection",
		noVideos: "There are currently no videos in your library.",
		displayFormat: "Display Format",
		portrait: "Portrait",
		landscape: "Landscape"
	},
	editVideoPage: {
		pageTitle: "Edit Video",
		dataUpdated: "Video updated successfully!",
		shoppableVideo: "Edit Video",
		generate: "Save",
		videoTitle: "Video Title",
		subTitle: "Subtitle",
		videoDescription: "Video Description",
		videoDescPlaceholder: "Enter a description for your video.",
		enterTitle: "Enter Title",
		buttonText: "Button Text",
		coverImageText: "Cover Image",
		enterButtonText: "Enter Button Text",
		videoButtonText: "Shop Now",
		videoSnippet: "Video Snippet",
		product: "Link",
		products: "Featured Links",
		productURL: "URL",
		phoneNumber: "Phone Number",
		emailAddress: "Email",
		addInteraction: "Add an Interaction",
		productsText: "Add product or page links below to feature them in your video.",
		walkthroughText: "Click here for walkthroughs on adding it to different website platforms.",
		walkthroughLink: process.env.WALKTHROUGH_URL_EN,
		uploadVideoText: "Drag an MP4 or",
		uploadingStatus: "Uploading",
		encodingPrepStatus1: "Hang tight!\nPreparing to optimize your video…",
		encodingPrepStatus2: "Starting up the optimize engine",
		encodingPrepStatus3: "Almost there!",
		encodingStatus: "Optimizing",
		videoSize: "1080px x 1920px",
		maxFileSize: "Maximum File Size 512MB",
		recommendedSize: "Recommended Size:",
		videoFormat: "MP4, MOV, AVI, or WMV Format",
		addImage: "Add Image",
		replaceCoverImage: "Cover Image",
		uploadCoverImage: "Cover Image",
		generatingCoverImage: "Generating Cover Image...",
		fetchingProductText: "Collecting link details…",
		uploadImageAltText: "Upload Image",
		collectionsSubheading: "Collections",
		addToCollectionText: "Add to Collection...",
		allVideosText: "All Videos",
		videosText: "Videos",
		displayTitle: "Display Title",
		displayCaptions: "Display Captions",
		performanceSubheading: "Performance",
		performanceNoData: "No performance data yet.",
		playsHeading: "Plays",
		clicksHeading: "Clicks",
		likesHeading: "Likes",
		playtimeHeading: "Playtime",
		linksClicksHeading: "Interactions:",
		phoneCalls: "Phone Calls",
		emailLeads: "Leads",
		engagementScoreHeading: "Engagement Score",
		percentageVideoPlayed: "% of Video Played",
		helpIconAlt: "Help",
		addAnotherVideo: "Create Another Video",
		engagementScoreDesc: "How engaging your videos are based on watch time and plays",
		displayFormat: "Display Format",
		portrait: "Portrait",
		landscape: "Landscape"
	},
	videoSnippet: {
		unpublished:
			"Add a video file, title, thumbnail image and page URL and product to generate snippet for your website.",
		snippetQuestion: "Have you added your core script yet?",
		yesAnswer: "Yes, I have",
		noAnswer: "No, I'll add it now"
	},
	dashboardPage: {
		upgradeToPro: "Upgrade to Pro to unlock Conversion Metrics.",
		onlyShopify: "Currently only available for Shopify stores.",
		groupVideos: "Group your videos to add to site",
		howToInstall: "How to install on your website",
		noVideoPerformance: "No video performance data yet.",
		noConversion: "No conversion data yet.",
		ensurEscriptSdded: "Ensure your conversion script is added to your website.",
		oneConversion: "Data will populate here only after you've had at least one conversion.",
		addVideo: "Add a video collection to your website to track performance data.",
		createVideo: "Create a Video",
		videoManager: "Video Manager",
		yourVideos: "Your Videos",
		noShoppableVideos: "You don't have any videos yet.",
		loadingIssue: "There was an issue loading your videos.",
		createOneNow: "Create One Now",
		title: "Title",
		products: "Links",
		dateCreated: "Date Created",
		plays: "Plays",
		clicks: "Clicks",
		likes: "Likes",
		score: "Score",
		carouselTextTitle: "Just copy and paste and you're set!",
		carouselText:
			"Copy the snippet after clicking the button below and paste it anywhere on your website to showcase all your videos in a carousel. It's simple, and you'll never need to make changes. Every video you create or edit will update in your carousel as you save it. You can also add individual videos without the carousel by clicking Add to Site on each video below.",
		addCarousel: "Add Carousel to Site",
		actions: "Actions",
		newVideo: "+ New Video",
		createCollection: "Create Collection",
		helpResources: "Help + Resources",
		talkToUs: "Talk to Us",
		interactiveVideo: "Create a New Video",
		organizeVideos: "Group + Organize Videos",
		addYourVideos: "Add Your Videos to Site",
		gotFeedback: "Got feedback? We'd love to hear from you!",
		updateButtonText: "Update",
		apProText: "Access more features and metrics with Pro",
		resourcesLink: process.env.RESOURCES_URL_EN,
		impressionsUsed: "Impressions used this month.",
		resetAt: "This will reset"
	},
	collectionsPage: {
		loadingIssue: "Loading issue.",
		createCollection: "Create Collection",
		title: "Title",
		videos: "Videos",
		dateCreated: "Date Created",
		actions: "Actions",
		allVideos: "All Videos (Default)",
		addMoreCollections: "Add more collections to organize your videos!"
	},
	signInPage: {
		subText: "Sign in to create and manage your interactive videos.",
		question: "Don't have an account?",
		createAccount: "Create one here.",
		imgTitle: "Leverage your existing social content on your website in minutes.",
		imgCopy:
			"You already have the content, it's time to get the most use out of it. Upload your videos you've already created and tag products, converting them into Interactive Videos that you can use on your website.",
		manualSignInText1: "We'll email you a link for a password-free sign in.",
		manualSignInText2: "Or you can",
		manualSignInText3: "manually sign in instead.",
		emailSent: "Email Sent!",
		checkInbox:
			"Please check your inbox for your sign in link in order to continue. If you didn't receive an email, check your junk and spam folders.",
		intuitText:
			"Click the link below to sign in with your Intuit account. If you don't have an account already, this button will create one for you after you sign in."
	},
	passwordResetPage: {
		resetPassword: "Reset Password",
		setPassword: "Set Password",
		subText: "Enter your email to receive a password reset email.",
		question: "Know your password?",
		checkInbox: "Reset Email Sent",
		checkInboxSubText: "Check your inbox to reset your password. Make sure to check your junk mail just in case.",
		backToSignIn: "Back To Sign In",
		newPassword: "New Password",
		confirmNewPassword: "Re-enter New Password",
		passwordNotMatch: "Passwords did not match",
		newPasswordText: "Enter New Password",
		newPasswordSubText: "You'll be logged in after you've entered your new password.",
		newPasswordQuestion: "Change your mind?",
		imgTitle: "Leverage your existing social content on your website in minutes.",
		imgCopy:
			"You already have the content, it's time to get the most use out of it. Upload your videos you've already created and tag products, converting them into Interactive Videos that you can use on your website."
	},
	notFoundPage: {
		errorCode: "404",
		errorTitle: "Page Doesn't Exist",
		errorMessage:
			"Looks like you've reached a dead end or a page that doesn't exist anymore. Try signing in again or going back."
	},
	performancePage: {
		pageTitle: "Performance",
		firstSectionHeading: "Video Performance",
		secondSectionHeading: "Top Performing Videos",
		impressionsHeading: "Impressions",
		playsHeading: "Plays",
		clicksHeading: "Clicks",
		playtimeHeading: "Playtime",
		scoreHeading: "Score",
		dateRangeHeading: "Date Range",
		refreshButton: "Refresh",
		engagementTabHeading: "Engagement",
		conversionTabHeading: "Conversion",
		enableShopifyText: "Enable on your Shopify Store to get conversion metrics.",
		totalOrdersHeading: "Orders",
		engagedSessionsHeading: "Engaged Sessions",
		averageBasketSizeHeading: "Average Basket Size",
		converstionRateHeading: "Conversion Rate",
		topVideosHeading: "Top Videos",
		topCollectionsHeading: "Top Converting Collections",
		videoTitleLabel: "Video",
		collectionTitleLabel: "Collection",
		playsTitleLabel: "Plays",
		ordersTitleLabel: "Orders",
		orderTitleLabel: "Order",
		cvrTitleLabel: "CVR"
	},
	videoLibraryPage: {
		pageTitle: "Video Library",
		pageSubtitle: "Upload videos to your library to use in your Interactive Videos.",
		uploadVideosText: "Drag Video Files or Click Below",
		uploadVideosSubtext: "For best results use files less than 512MB",
		uploadingStatus: "Uploading...",
		processingStatus: "Processing..."
	},
	plansPage: {
		pageTitle: "Plans + Pricing",
		pageSubtitle: "Plans that scale to your needs.",
		freePlanDescription: "Best for brands expanding their focus on video.",
		proPlanDescription: "Unlock more features + engagement metrics with Pro.",
		enterprisePlanDescription: "Transform your website into a video-first experience.",
		apFreePrice: "Free",
		apProMonthPrice: "$29 / month",
		apMonthPrice: " / month",
		currentPlan: "Current Plan",
		startFreeTrial: "Start Free Trial",
		free14Days: "14 Days Free",
		apProIncludes: "INCLUDES",
		adFreeVideoPlayer: "Ad-free Video Player",
		videoLinkEmbed: "Video Link Embed",
		standardVideoDisplayOptions: "Standard Video Display Options",
		basicPerformanceMetrics: "Basic Performance Metrics",
		everythingInBasicPlus: "Everything in Basic, plus:",
		removeLogo: "Remove Logo",
		socialEngagementFeatures: "Social Engagement Features",
		landscapeVideoDisplay: "Landscape Video Display",
		videoPlayerCustomization: "Video Player Customization",
		additionalDisplayOptions: "Additional Display Options",
		advancedPerformanceMetrics: "Advanced Performance Metrics",
		conversionTrackingShopifyOnly: "Conversion Tracking (Shopify Only)",
		everythingInProPlus: "Everything in Pro, plus:",
		dedicatedCustomerSuccessManager: "Dedicated Customer Success Manager",
		contentPlusGoToMarketStrategySessions: "Content + Go-to-Market Strategy Sessions",
		apSupportedImplementation: "Supported Implementation",
		ongoingOptimizationSupport: "Ongoing Optimization Support",
		customLimitOnVideoImpressions: "Custom Limit on Video Impressions",
		prioritySupport: "Priority Support",
		shareableVideoLinks: "Shareable Video Links",
		emailEmbedWithGif: "Email Embed with GIF",
		proExpiryText1: "You will return to basic in",
		proExpiryText2: "days",
		customImpressionLimit: "Custom Impression Limit",
		contactUs: "Contact Us"
	},
	modals: {
		titleLibrary: "Library",
		titleFromVideo: "From Video",
		titleUpload: "Upload",
		titleInstagram: "Instagram",
		titleLink: "Link",
		coverImageTitle: "Choose Cover Image",
		createVideo: "Create Video",
		setCoverImage: "Set Cover Image",
		dragVideoFile: "Drag Video File or Click Below",
		dragVideoFileLine1: "For best results use an MP4 less than",
		dragVideoFileLine2: "512MB sized to 720px x 1280px",
		titleLinkText: "Add your own link for a video",
		linkBodyText1: "Paste a link to a video on Dropbox or a CDN to use for",
		linkBodyText2: "your Interactive Video. Make sure the video link is public.",
		linkPlaceholder: "Paste URL",
		clickImage: "Click on image above to upload a new file.",
		thumbnailText: "Drag thumbnail below to select frame.",
		confirmLeavingTitle: "Leave Without Saving?",
		confirmLeavingText: "You haven't saved your changes. If you continue you will lose any progress you've made.",
		signOutModalText: "You're about to sign out of your account. You'll need to sign back in to continue.",
		deleteVideoModalTitle: "Delete Video?",
		deleteVideoModalText: "You can not undo this action. This video will be removed from your Collection.",
		deleteVideoConfirmation: "Video was deleted.",
		deletePaymentMethod: "Delete Payment Method",
		deleteCollectionModalTitle: "Delete Collection?",
		deletePaymentMethodTitle: "Delete Payment Method?",
		deletePaymentModalText: "Your Payment Method will be deleted",
		deleteCollectionModalText:
			"This collection will be deleted but the videos within will still be available in your Video Manager.",
		deleteCollectionConfirmation: "Collection was deleted.",
		removeUserModalTitle: "Remove User?",
		removeUserModalText: "They won't be able to access this Company anymore.",
		removeUserModalButton: "Remove",
		removeUserConfirmation: "User was removed.",
		inviteUserModalTitle: "Add New User",
		inviteUserModalText: "They'll be able to access their accounts after they verify their email address.",
		inviteUserModalButton: "Send Invite",
		inviteUserConfirmation: "Invite sent.",
		removeKeyConfirmation: "Key was removed.",
		helpCentreLink: process.env.HELP_CENTRE_URL_EN,
		helpCentre: "Help Centre",
		helpCentreText: "Access our library of support resources and training materials.",
		contactUs: "Contact Us",
		contactUsText: "Still having trouble? Let us know! We're happy to help!",
		addToSite: "Add to Site",
		addStep1: "First add the script to your website header.",
		addStep1Text: "You only do this once so ignore this step if you've already added it.",
		addStep2: "Add your carousel snippet where you want it to display.",
		addStep2Text:
			"This will showcase all your videos within one carousel. Any updates or new videos you create in your admin will automatically be displayed in your carousel.",
		addStep2Video: "Add your video snippet where you want it to display.",
		addStep2TextVideo:
			"You will need to add a snippet for each video. Alternately you can add a carousel from your Video Manager that displays all your videos with one snippet.",
		addStep2Widget: "Add your widget to the pages you want it to display on.",
		addStep2WidgetText:
			"This will showcase all your videos within the widget. It will start with the first video and play through all the videos in the collection.",
		addStep2OnSite: "Add your video snippet anywhere you want to display it.",
		addStep2OnSiteText:
			"This will showcase all your videos within the player on the web page. It will start with the first video and play through all the videos in the collection.",
		selectVideos: "Select Videos",
		selectVideosList: "Select the video(s) you want to add to this Collection.",
		updateCollection: "Update Collection",
		removeCollectionVideo:
			"This video will be removed from this Collection but will still be available in your Video Manager.",
		removeVideo: "Remove Video?",
		snippetOptsStep1: "Step 1 of 2: ",
		snippetOptsStep1Text: "Select Display Type",
		snippetOptsStep1Subtext:
			"Select the way you want your video(s) to display to continue. Don't worry, you can use all of these simultaneously.",
		snippetOptsStep2: "Step 2 of 2: ",
		snippetOptsStep2Text: "Add to Site",
		snippetOptsStep2Subtext: "",
		snippetOpt1: "Video Carousel",
		snippetOpt2: "Widget",
		snippetOpt3: "Inline",
		goBackBtnText: "Go Back",
		aboutYou: "Tell us a bit about you",
		extraInfoText: "We'll make sure you get the experience that's best for you.",
		doThisLater: "Do This Later",
		yourName: "What should we call you?",
		yourCompany: "What's your company/business name?*",
		yourTeam: "What team are you on?*",
		yourWebsite: "What website platform do you use?*",
		enterPasswordModalTitle: "Enter Password",
		enterPasswordInputPlaceholder: "Enter Passcode",
		generateApiKey: "Generate API Key",
		generateApiKeyText: "Click Generate to create your API Key.",
		newApiKey: "New API Key",
		newApiKeyText:
			"Copy the API Key to add to your website. You won't be able to view this again so make sure you paste your key somewhere safe.",
		revokeAccessText: "This will revoke access to connected platforms",
		deleteKey: "Delete Key",
		deleteVideo: "Delete Video?",
		deleteVideoText: "This video will be removed from your library.",
		checkEmailTitle: "Check your email",
		checkEmailText: "You'll receive a link to set your password.",
		changePlanTitle: "Are you sure?",
		changePlanText: "Your new plan will start immediately",
		keepCurrentPlan: "I changed my mind",
		confirmUpgrade: "Confirm Selection",
		downgradePlanText: "Your plan will change after current usage cycle",
		KeepSubscription: "Keep Pro Subscription",
		CloseWindow: "Close this window",
		CancelledPlanTitle: "You've cancelled your Pro Subscription",
		CancelledPlanText: "You can re-activate and access Pro again at anytime.",
		apProPlanFrequency1: "Monthly",
		apProPlanFrequency2: "Annual",
		apProPlanFrequency2Bonus: "1-Month Free",
		apProBasicPlan: "Basic",
		apProProPlan: "Pro",
		apProBasicPlanDescription:
			"Everything you need to get started with video on your website. Access to the full video creator and manager lets you boost engagement on your website in minutes.",
		apProProPlanDescription:
			"Best for medium-large sized businesses looking to do more with video. Access the full basic tier features with added metrics and reporting. Get first access to new features as they get released.",
		apProFree14Days: "Free for 14 Days",
		apProFreeForever: "Free Forever",
		apProMonthPriceShort: "$29.00 USD",
		apProUndiscountedPrice: "$29.00 USD / month",
		apProDiscountedPrice: "$90.75 USD / month",
		apAnnuallyProPrice: "$1,089.00 USD Billed Annually",
		apProIncludes: "INCLUDES",
		apProSelectPro: "Start Free Trial",
		apProUnlimitedSeats: "Unlimited Seats",
		apProBulkVideoUpload: "Bulk Video Upload",
		apProInteractiveVideoCreation: "Interactive Video Creation",
		apPro10LinksPerVideo: "10 Links per Video",
		apProCarouselSnippetInlinePlayerEmbed: "Carousel, Snippet + Inline Player Embed",
		apProShopifyApp: "Shopify App",
		apProPerformanceDashboard: "Performance Dashboard",
		apProVideoPerformanceMetrics: "Video Performance Metrics",
		apPro14DayFreeTrialOfPro: "14 Day free trial of Pro",
		apProEverythingInBasic: "Everything In Basic",
		apProEnagementMetrics: "Enagement Metrics",
		apProConversionTracking: "Conversion Tracking",
		apProWhitelabelVideoPlayer: "Whitelabel Video Player",
		apProDedicatedSuccessContact: "Dedicated Success Contact",
		apProStartTrial: "Start 14-Day Free Trial",
		apProSavePaymentDetails: "Save payment details",
		apProContinueToDashboard: "Continue to Dashboard",
		apProCongrats: "Congrats! 🎉",
		apProUpgraded: "Your Pro trial has started.",
		apProUpgradedSubtext1: "We're so excited to have you!",
		apProUpgradedSubtext2:
			"Take a look around and get familiar with the new features - we're always here if you have any questions.",
		apProToday: "TODAY",
		apProTodaySubtext: "You can start using Pro worry-free for 14 days!",
		apProTodaySubtext2: "Pro Subscription begins.",
		apProPlanBegins: "Your Pro plan begins. Make sure you add your payment method before your trial runs out.",
		apProPlanBegins2: "Your next payment comes out.",
		apProPlanMonth:
			"Your Pro plan begins. Make sure you add your payment method before your trial runs out. If payment details have been added, you will be charged $29.00 USD.",
		apProPlanAnnual:
			"Your Pro plan begins. Make sure you add your payment method before your trial runs out. If payment details have been added, you will be charged $1,089.00 USD.",
		apProFeature1: "Unlock new Metrics",
		apProFeature2: "Track Your Conversion",
		apProFeature3: "Whitelabel Solution",
		apProFeature1Subtext: "Understand what videos keep your viewers engaged and where they could use improvement.",
		apProFeature2Subtext: "Understand how your videos are converting with our Shopify Conversion Integration.",
		apProFeature3Subtext:
			"Make it your own - we'll remove the branding displayed on your Interactive Videos.",
		doLater: "Do this later",
		stripeCardNumber: "Card Number",
		stripeExpiration: "Expiration",
		stripeCvc: "CVC",
		stripeCountry: "Country",
		stripePostalCode: "Postal Code",
		stripeZipCode: "Zip Code",
		stripePostalCodeError: "Please enter a valid postal code for your country.",
		stripePostalCodePlaceholder: "A1A 1A1",
		stripeZipCodePlaceholder: "12345",
		unlockNewFeatures: "Unlock New Features",
		welcomeToPro: "Welcome to Pro! ⭐",
		allSet: "You're all set.",
		addConversionScript: "Adding Your Shopify Conversion Script",
		addConversionLink: "https://www.youtube.com/watch?v=KcGOTeUrX3U",
		proTrialNowActive: "Check out these resources below to get familiar with your new tools.",
		backToDashboard: "Or, hop back into your dashboard to create new videos and collections to add to your website!",
		returnToDashboard: "Return to Dashboard",
		welcomeToProText:
			"You're in! Add your payment method to keep your plan active after your trial ends. You can do this anytime.",
		notRequirePayment: "Try out Pro free for 14 days - no payment details required, no strings attached!",
		free14Days: "free for 14 days.",
		freeDaysTag: "14 Days Free",
		firstBilling: "first billing after trial on",
		billedMonthly: "Billed Monthly",
		youUnlock: "With Pro, You Can:",
		shopifyOnly: "Shopify Only",
		showMore: "+ more",
		showLess: "show less",
		proFeature1: "📈 Capture Likes + Engagement Metrics",
		proFeature2: "🤑 Track Your Conversions",
		proFeature3: "👻 Remove Branding",
		proFeature4: "🛎️ Dedicated Support + Success Team",
		proFeature5: "🚀 First Access to New Features + Releases",
		upgradeYourAccount: "Upgrade your account",
		reachedMonthlyLimit: "You've reached your monthly impressions limit ",
		reachedMonthlyLimit2:
			"within your plan. Upgrade now to continue creating videos and increase your limits! Our flexible plans will provide an option that's best for you.",
		tonsOfPeople: "Tons of people are seeing your amazing content!",
		reachedImpressions: "So much so, that you've reached your impressions limit ",
		reachedImpressions2:
			"with your plan. Upgrade now to prevent any interruptions to your experience! Our flexible plans will provide an option that's best for you.",
		plansPricing: "Plans + Pricing",
		share: "Share",
		emailEmbed: "Email Embed",
		shareYourCollection: "Share your video collection",
		shareYourVideo: "Share your video",
		copyLink: "Copy Link",
		copyLinkText: "Just copy the link and share via social, email, text, website links, and more.",
		copyThumbnailText: "Copy thumbnail and paste it into your email.",
		copyThumbnail: "Copy Thumbnail",
		embedVideo: "Embed Video",
		embedVideoSize: "You can adjust the display size by changing the values in width + height in the snippet above.",
		editCaptions: "Edit Captions",
		setCaptions: "Set Captions",
		setSegment: "Set Segment",
		backgroundColor: "Background Color",
		textColor: "Text Color",
		textSize: "Text Size"
	}
};
