{"mongo": {"mongoProtocol": "mongodb", "mongoHost": "localhost:27017", "mongoUsername": "<PERSON><PERSON><PERSON>", "mongoPassword": "password"}, "sendGrid": {"fromAddress": "no-reply@localhost", "apiKey": "1234", "host": "http://localhost:5005"}, "hashkey": {"key": "supersecret"}, "storage": {"bucketName": "local-gpcdn.localhost", "tempBucketName": "local-gp-temp-video-uploads", "host": "http://localhost:5006", "isLocal": true}, "cdn": {"host": "http://localhost:5001/"}, "player": {"host": "http://localhost:5003/"}, "iframely": {"host": "https://iframe.ly", "apiKey": "api-key-provided-by-if<PERSON><PERSON>"}, "stripe": {"privateKey": "sk_", "webhookSecret": "whsec_1234"}, "cron": {"privateKey": "some-private-key"}, "oidc": [{"provider": "intuit", "userInfoEndpoint": "https://pingidc.com/userinfo", "jwksEndpoint": "https://pingidc.com/jwks", "tokenEndpoint": "https://pingidc.com/token", "clientId": "client-id-1", "clientSecret": "client-secret", "redirectEndpoint": "http://localhost/sign-in/intuit"}, {"provider": "google", "userInfoEndpoint": "https://google.com/userinfo", "jwksEndpoint": "https://google.com/jwks", "tokenEndpoint": "https://google.com/token", "clientId": "client-id-2", "clientSecret": "client-secret2", "redirectEndpoint": "https://localhost/sign-in/intuit"}], "speechToText": {"bucketName": "some-bucket-name", "apiEndpoint": "speech.googleapis.com", "storageUrl": "https://storage.googleapis.com", "credentials": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}, "vertex": {"credentials": **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}}