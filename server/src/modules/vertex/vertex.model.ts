import { GoogleAuth } from "google-auth-library";
import { getSecrets } from "../secrets/secrets.model";
import axios from "axios";

export class VertexModel {

	private projectId = process.env.GCP_PROJECT_ID;
	private region = process.env.GCP_CLOUD_RUN_REGION;
	private model = process.env.AI_MODEL;

	constructor() {
		if (!this.projectId) {
			throw new Error("Environment variable GCP_PROJECT_ID is required but not set.");
		}
		if (!this.region) {
			throw new Error("Environment variable GCP_CLOUD_RUN_REGION is required but not set.");
		}
		if (!this.model) {
			throw new Error("Environment variable AI_MODEL is required but not set.");
		}
	}

	/**
	 * @param prompt - The text prompt to send to the AI.
	 * @param temperature - Response randomness (0 = focused, 1 = creative).
	 * @param maxTokens - The maximum number of tokens.
	 * @returns AI-generated response text.
	 */
	public async getAIResponse(prompt: string, temperature: number, maxTokens: number): Promise<string> {

		try {
			const secrets = await getSecrets();

			const auth = new GoogleAuth({
				credentials: secrets.vertex.credentials,
				scopes: "https://www.googleapis.com/auth/cloud-platform"
			});

			const client = await auth.getClient();
			const accessToken = await client.getAccessToken();
			const url = (
				`https://${this.region}-aiplatform.googleapis.com/v1/projects/` +
				`${this.projectId}/locations/${this.region}/publishers/google/models/` +
				`${this.model}:generateContent`
			);

			const requestBody = {
				contents: [
					{
						role: "user",
						parts: [
							{ text: prompt }
						]
					}
				],
				generationConfig: {
					temperature: temperature,
					maxOutputTokens: maxTokens
				}
			};

			const response = await axios.post(url, requestBody, {
				headers: {
					Authorization: `Bearer ${accessToken.token}`,
					"Content-Type": "application/json"
				}
			});

			const generatedText = response.data?.candidates?.[0]?.content?.parts?.[0]?.text || "No AI response.";
			return generatedText;

		} catch (error: any) {
			console.error("Vertex AI error:", error?.response?.data || error.message);
			return "Failed to get AI response.";
		}
	}
}
