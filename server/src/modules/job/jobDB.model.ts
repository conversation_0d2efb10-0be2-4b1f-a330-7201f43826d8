import mongoose, { Schema } from "mongoose";
import { <PERSON><PERSON><PERSON> } from "./job.interfaces";
import { JobsType } from "./jobs.enums";
import { ReportRange } from "../reports/generate/reports.types";

const JobSchema: Schema = new Schema({
	tempFilename: {
		type: String,
		unique: true,
		validate: {
			validator: function(this: any, v: string): boolean {
				if (this.type === JobsType.REPORTS_AGGREGATE) {
					return true;
				}

				return typeof v === "string" && v.trim().length > 0;
			},
			message: `tempFilename is required unless type is '${JobsType.REPORTS_AGGREGATE}'.`
		}
	},
	accountId: { type: Schema.Types.ObjectId, required: false },
	userId: { type: Schema.Types.ObjectId, required: false },
	type: {
		type: String,
		required: true,
		enum: Object.values(JobsType)
	},
	status: { type: String, required: true },
	statusMessage: { type: String, required: true },
	createdAt: { type: Number, default: () => Date.now() },
	updatedAt: { type: Number, default: () => Date.now() },
	sourceURL: { type: String, required: false },
	progressPercent: { type: Number, required: false },
	nextStatusCheck: { type: Number, required: false },
	videoId: { type: Schema.Types.ObjectId, required: false },
	imageWidthPx: { type: Schema.Types.Number, required: false },
	imageHeightPx: { type: Schema.Types.Number, required: false },
	imageURL: { type: Schema.Types.String, required: false },
	callbackInfo: { type: { callbackUrl: String, callbackData: String }, required: false },
	reportRange: {
		type: String,
		validate: {
			validator: function(this: any, v: string): boolean {
				if (this.type === JobsType.REPORTS_GENERATE) {
					if (typeof v !== "string" || v.trim().length === 0) return false;
					const normalized = v.toUpperCase() as ReportRange;
					return Object.values(ReportRange).includes(normalized);
				}
				return true;
			},
			message: "reportRange is required and must be a valid ReportRange"
		},
		required: function(this: any): boolean {
			return this.type === JobsType.REPORTS_GENERATE;
		}
	},
	reportDate: {
		type: Date,
		validate: {
			validator: function(this: any, v: Date): boolean {
				if (this.type === JobsType.REPORTS_GENERATE) {
					return v instanceof Date && !isNaN(v.getTime());
				}
				return true;
			},
			message: "reportDate is required and must be a valid Date"
		},
		required: function(this: any): boolean {
			return this.type === JobsType.REPORTS_GENERATE;
		}
	},
	aggregateDate: {
		type: Date,
		validate: {
			validator: function(this: any, v: Date): boolean {
				if (this.type === JobsType.REPORTS_AGGREGATE) {
					return v instanceof Date && !isNaN(v.getTime());
				}
				return true;
			},
			message: "aggregateDate is required and must be a valid Date"
		},
		required: function(this: any): boolean {
			return this.type === JobsType.REPORTS_AGGREGATE;
		}
	}
},
{ timestamps: true }
);

export const JobDBModel = mongoose.model<IJob>(
	"Jobs",
	JobSchema
);
