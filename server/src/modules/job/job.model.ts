import mongoose, {
	ClientSession,
	FilterQuery
} from "mongoose";
import { APIErrorName } from "../../interfaces/apiTypes";
import { APIError } from "../../utils/helpers/apiError";
import { JobDBModel } from "./jobDB.model";
import {
	JobsStatus,
	JobsType
} from "../job/jobs.enums";
import { IJob } from "./job.interfaces";
import { AccountMetricModel } from "../../modules/account/account.metric.model";
import { VideoEncodeModel } from "../video/encode/video.encode.model";
import { ReportsAggregateModel } from "../reports/aggregate/aggregate.model";
import { ReportsGenerateModel } from "../reports/generate/reports.model";

export class JobModel {
	private session: ClientSession | null;
	constructor (session: ClientSession | null) {
		this.session = session;
	}

	public static async createIndexes (): Promise<void> {
		await JobDBModel.collection.createIndexes([
			{ key: { tempFilename: 1 }, name: "tempFilename_index", unique: true }
		]);
	}

	public static async getIndexes (): Promise<unknown> {
		return await JobDBModel.collection.indexes();
	}

	async readOneByTempFilename (tempFilename: string): Promise<IJob> {
		const filter: FilterQuery<IJob> = {
			tempFilename: tempFilename
		};
		const document = await JobDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}
		return document;
	}

	async readOneById (_id: string): Promise<IJob> {
		const filter: FilterQuery<IJob> = {
			_id: new mongoose.Types.ObjectId(_id)
		};
		const document = await JobDBModel.findOne(filter).session(this.session);
		if (!document) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}
		return document;
	}

	async runJobById (jobId: string): Promise<void> {
		const job = await this.readOneById(jobId);

		if (job.type === JobsType.ENCODE_VIDEO) {
			const videoEncodeModel = new VideoEncodeModel(job);
			await videoEncodeModel.startEncoder();
		} else if (job.type === JobsType.SYNC_ACCOUNTS) {
			const accountMetricModel = new AccountMetricModel(null);
			await accountMetricModel.computeDailyWeeklyMetrics();
		} else if (job.type === JobsType.REPORTS_AGGREGATE) {
			const reportsAggregateModel = new ReportsAggregateModel(this.session);
			await reportsAggregateModel.aggregateAccounts(job);
		} else if (job.type === JobsType.REPORTS_AGGREGATE_SINCE) {
			const reportsAggregateModel = new ReportsAggregateModel(this.session);
			await reportsAggregateModel.aggregateAccounts(job);
		} else if (job.type === JobsType.REPORTS_GENERATE) {
			const reportsModel = new ReportsGenerateModel(this.session);
			await reportsModel.processAccounts(job);
		} else {
			throw new APIError(APIErrorName.E_INVALID_INPUT, `Unsupported job type ${job.type}`);
		}
	}

	public async createJob (data: Partial<IJob>): Promise<IJob> {
		const options = { session: this.session };
		const job = new JobDBModel(data);
		const savedJob = await job.save(options);
		return savedJob;
	}

	public async updateJobStatus (jobId: string, status: JobsStatus, statusMessage: string): Promise<IJob> {
		const filter: FilterQuery<IJob> = { _id: new mongoose.Types.ObjectId(jobId) };
		const update = {
			status: status,
			statusMessage: statusMessage,
			updatedAt: Date.now()
		};

		const options = { session: this.session, new: true };

		const updatedJob = await JobDBModel.findOneAndUpdate(filter, update, options);
		if (!updatedJob) {
			throw new APIError(APIErrorName.E_DOCUMENT_NOT_FOUND, "Job not found");
		}

		return updatedJob;
	}
}


