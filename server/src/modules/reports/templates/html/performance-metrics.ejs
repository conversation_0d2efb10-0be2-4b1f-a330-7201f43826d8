<% 
    function formatPlaytime(seconds) {
        seconds = Number(seconds) || 0;
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        let result = '';
        if (h > 0) result += `${h}h `;
        if (m > 0 || h > 0) result += `${m}m `;
        result += `${s}s`;
        return result.trim();
    }
%>

<div class="section-title">Performance</div>

<div class="metrics-grid">
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">IMPRESSIONS</span>
            <span class="metric-change <%= metrics.impressions.change >= 0 ? 'positive' : 'negative' %>">
                <%= metrics.impressions.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.impressions.change) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon impressions">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/impressions-icon.svg" />
            </div>
            <div class="metric-value"><%= metrics.impressions.value %></div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">PLAYS</span>
            <span class="metric-change <%= metrics.plays.change >= 0 ? 'positive' : 'negative' %>">
                <%= metrics.plays.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.plays.change) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon plays">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/plays-icon.svg" />
            </div>
            <div class="metric-value"><%= metrics.plays.value %></div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">CLICKS</span>
            <span class="metric-change <%= metrics.clicks.change >= 0 ? 'positive' : 'negative' %>">
                <%= metrics.clicks.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.clicks.change) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon clicks">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/clicks-icon.svg" />
            </div>
            <div class="metric-value"><%= metrics.clicks.value %></div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">EMAILS</span>
            <span class="metric-change <%= metrics.emails.change >= 0 ? 'positive' : 'negative' %>">
                <%= metrics.emails.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.emails.change) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon emails">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/emails-icon.svg" />
            </div>
            <div class="metric-value"><%= metrics.emails.value %></div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">CALLS</span>
            <span class="metric-change <%= metrics.calls.change >= 0 ? 'positive' : 'negative' %>">
                <%= metrics.calls.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.calls.change) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon calls">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/phone-icon.svg" />
            </div>
            <div class="metric-value"><%= metrics.calls.value %></div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">ENGAGED SESSIONS</span>
            <span class="metric-change <%= metrics.engaged.change >= 0 ? 'positive' : 'negative' %>">
                <%= metrics.engaged.change >= 0 ? '↑' : '↓' %> <%= Math.abs(metrics.engaged.change) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon engaged">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/engaged-sessions-icon.svg" />
            </div>
            <div class="metric-value"><%= metrics.engaged.value %></div>
        </div>
    </div>

    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-label">PLAYTIME</span>
            <span class="metric-change <%= String(metrics.playtime.change).includes('-') ? 'negative' : 'positive' %>">
                <%= String(metrics.playtime.change).includes('-') ? '↓' : '↑' %> <%= formatPlaytime(Math.abs(metrics.playtime.change)) %>
            </span>
        </div>
        <div class="metric-value-row">
            <div class="metric-icon playtime">
                <img alt="logo" class="logo" width="30" src="<%=cdnHost%>reports/playtime-icon.svg" />
            </div>
            <div class="metric-value"><%= formatPlaytime(metrics.playtime.value) %></div>
        </div>
    </div>


</div>
