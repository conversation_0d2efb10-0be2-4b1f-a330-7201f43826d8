<% 
    function formatDateRange(fromDate, toDate, withYear = false) {
        if (!fromDate || !toDate) return '';
        const options = withYear ? { month: 'long', day: 'numeric', year: 'numeric' } : { month: 'long', day: 'numeric' };
        const from = new Date(fromDate).toLocaleDateString('en-US', options);
        const to = new Date(toDate).toLocaleDateString('en-US', options);
        return `${from} - ${to}`;
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Report</title>
    <script src="<%= cdnHost %>/scripts/chart.js"></script>
    <%- include('../shared/styles') %>
    <%- include('styles') %>
</head>
<body>
    <div class="report-container">
        <%- include('../shared/header', {
            reportType: 'WEEKLY REPORT',
            reportPeriod: formatDateRange(fromDate, toDate, true)
        }) %>

        <div class="content">
            <div class="title">Your weekly performance metrics from <%= formatDateRange(fromDate, toDate, false) %> are here.</div>

            <%- include('performance-metrics', { metrics: metrics }) %>

            <%- include('daily-playtime-chart', {
                chartData: dailyPlaytimeData,
                chartId: 'dailyPlaytimeChart'
            }) %>

            <%- include('daily-performance-chart', {
                chartData: dailyPerformanceData,
                chartId: 'dailyPerformanceChart'
            }) %>

            <%- include('daily-plays-chart', {
                chartData: dailyPlaysData,
                chartId: 'dailyPlaysChart'
            }) %>

            <%- include('metrics-grid', { metricsData: metricsData }) %>

            <%- include('video-performance-table', { videos: videos }) %>

            <%- include('ai-insights', { insightSummary: insightSummary }) %>
        </div>
    </div>
</body>
</html>
