<style>
	body {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
		margin: auto;
		padding: 20px;
		background-color: #000000;
		line-height: 1.4;
		max-width: 1000px;
	}

	.report-container {
		box-shadow: none;
		border-radius: 0;
		background-color: #ffffff;
		max-width: none;
		width: 100%;
	}

	.positive-color {
		color: #0f9d58;
	}

	.negative-color {
		color: #d93025;
	}

	/* General Layout */
	.header {
		background: white;
		padding: 30px;
		border-bottom: 2px solid #000000;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.content {
		padding: 30px;
	}

	.report-period {
		text-align: right;
		color: #000000;
	}

	.report-period .dates {
		font-size: 14px;
		color: #4285f4;
		font-weight: 500;
	}

	.title {
		font-size: 18px;
		font-weight: 600;
		color: #333;
		margin-bottom: 30px;
	}

	.section-title {
		font-size: 16px;
		font-weight: 600;
		color: #333;
		margin-bottom: 20px;
		margin-top: 40px;
	}

	/* Metrics */
	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
		gap: 20px;
		margin-bottom: 30px;
	}

	.metric-card {
		background: #fafafa;
		border-radius: 10px;
		padding: 20px;
		position: relative;
	}

	.metric-header,
	.additional-metric-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8px;
	}

	.metric-value-row {
		display: flex;
		align-items: center;
		gap: 12px;
	}

	.metric-label,
	.additional-metric-label {
		font-size: 11px;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		color: #666;
	}

	.metric-value {
		font-size: 18px;
		font-weight: 700;
		color: #333;
		line-height: 1;
	}

	.metric-change,
	.additional-metric-change {
		font-size: 10px;
		font-weight: 600;
		padding: 2px 6px;
		border-radius: 4px;
	}

	.additional-metric-change.positive {
		color: #0f9d58;
        background: #e8f5e8;
	}

	.additional-metric-change.negative {
		color: #d93025;
        background: #fce8e6;
	}

	.metric-icon {
		width: 30px;
		height: 30px;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 12px;
		color: white;
		flex-shrink: 0;
	}

	.additional-metrics {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 20px;
		margin-bottom: 40px;
	}

	.additional-metric-card {
		background: #fafafa;
		border-radius: 10px;
		padding: 20px;
	}

	.additional-metric-value {
		font-size: 24px;
		font-weight: 700;
		color: #333;
		line-height: 1;
	}

	.additional-metric-description {
		font-size: 10px;
		color: #999;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		margin-top: 4px;
	}

	/* Chart Section */
	.chart-container {
		margin-top: 20px;
		margin-bottom: 40px;
		position: relative;
		height: 350px;
		width: 100%;
	}

	.chart-title {
		font-size: 14px;
		font-weight: 600;
		color: #333;
		margin-bottom: 10px;
	}

	.chart-subtitle {
		font-size: 12px;
		color: #666;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		margin-bottom: 15px;
		height: 20px;
	}

	.chart-canvas {
		height: 300px !important;
		width: 100% !important;
		max-height: 300px;
		display: block;
	}

	.chart-legend {
		display: flex;
		justify-content: center;
		gap: 20px;
		margin-bottom: 15px;
		flex-wrap: wrap;
	}

	.legend-item {
		display: flex;
		align-items: center;
		gap: 6px;
		font-size: 12px;
		color: #666;
	}

	.legend-color {
		width: 12px;
		height: 12px;
		border-radius: 50%;
	}

	/* Video Table */
	.video-table {
		width: 100%;
		border-collapse: separate;
		border-spacing: 0 0.7em;
		margin-bottom: 40px;
		font-size: 14px;
	}

	.video-table th {
		padding: 12px 8px;
		text-align: left;
		font-weight: 600;
		font-size: 11px;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		color: #000000;
	}

	.video-table tbody tr {
		background: #fafafa;
		border-radius: 10px;
	}

	.video-table td {
		padding: 12px 8px;
		vertical-align: middle;
	}

	.video-position {
		font-weight: 700;
		font-size: 16px;
		color: #333;
		width: 40px;
	}

	.video-position-change {
		font-size: 10px;
		font-weight: 600;
		margin-left: 4px;
	}

	.video-position-change.up {
		color: #0f9d58;
	}

	.video-position-change.down {
		color: #d93025;
	}

	.video-position-change.same {
		color: #666;
	}

	.video-info {
		display: flex;
		align-items: center;
	}

	.video-thumbnail {
		width: 40px;
		height: 30px;
		border-radius: 4px;
		background: #ddd;
		margin-right: 12px;
		flex-shrink: 0;
	}

	.video-thumbnail img {
		width: 100%;
		height: 100%;
		object-fit: cover;
		border-radius: 4px;
	}

	.video-title {
		font-weight: 500;
		color: #333;
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	/* AI Insights */
	.ai-insights {
		background: #1a1a1a;
		color: white;
		border-radius: 12px;
		padding: 24px;
		margin-top: 40px;
	}

	.ai-insights-header {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 16px;
	}

	.ai-insights-icon {
		font-size: 20px;
	}

	.ai-insights-title {
		font-size: 16px;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 1px;
	}

	.ai-insights-content p {
		margin-bottom: 12px;
	}

	.ai-insights-content p:last-child {
		margin-bottom: 0;
	}

	/* Responsive */
	@media (max-width: 768px) {
		.header {
			flex-direction: column;
			text-align: center;
			gap: 15px;
		}

		.metrics-grid,
		.additional-metrics {
			grid-template-columns: 1fr;
		}

		.content {
			padding: 20px;
		}

		.chart-legend {
			gap: 10px;
		}

		.video-table {
			font-size: 12px;
		}

		.video-table th,
		.video-table td {
			padding: 8px 4px;
		}

		.video-title {
			max-width: 120px;
		}
	}

	@media (max-width: 600px) {
		.video-table {
			display: block;
			overflow-x: auto;
			white-space: nowrap;
		}
	}
</style>
