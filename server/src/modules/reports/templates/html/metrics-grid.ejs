<% 
    function formatPlaytime(seconds) {
        seconds = Number(seconds) || 0;
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        let result = '';
        if (h > 0) result += `${h}h `;
        if (m > 0 || h > 0) result += `${m}m `;
        result += `${s}s`;
        return result.trim();
    }
%>

<div id="metricsSection" class="section-title">Metrics</div>

<div class="additional-metrics">
    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">ENGAGEMENT RATE</span>
            <span class="additional-metric-change <%= metricsData.engagementRate.change >= 0 ? 'positive' : 'negative' %>">
                <%= metricsData.engagementRate.change >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData.engagementRate.change.toFixed(2)) %>%
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData.engagementRate.value.toFixed(2) %>%</div>
        <div class="additional-metric-description">IMPRESSIONS / ENGAGED SESSIONS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">PLAY RATE</span>
            <span class="additional-metric-change <%= metricsData.playRate.change >= 0 ? 'positive' : 'negative' %>">
                <%= metricsData.playRate.change >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData.playRate.change.toFixed(2)) %>%
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData.playRate.value.toFixed(2) %>%</div>
        <div class="additional-metric-description">IMPRESSIONS / PLAYS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">CLICKTHROUGH RATE</span>
            <span class="additional-metric-change <%= metricsData.clickthroughRate.change >= 0 ? 'positive' : 'negative' %>">
                <%= metricsData.clickthroughRate.change >= 0 ? '↑' : '↓' %><%= Math.abs(metricsData.clickthroughRate.change.toFixed(2)) %>%
            </span>
        </div>
        <div class="additional-metric-value"><%= metricsData.clickthroughRate.value.toFixed(2) %>%</div>
        <div class="additional-metric-description">PLAYS / CLICKS</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">LEAD COUNT</span>
            <span class="additional-metric-change positive">↑<%= Math.round(metricsData.leadCount.change) %></span>
        </div>
        <div class="additional-metric-value"><%= Math.round(metricsData.leadCount.value) %></div>
        <div class="additional-metric-description">LEADS TO DATE</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">PLAYS PER SESSION</span>
            <span class="additional-metric-change <%= metricsData.playsPerSession.change >= 0 ? 'positive' : 'negative' %>">
                <%= metricsData.playsPerSession.change >= 0 ? '↑' : '↓' %><%= Math.round(Math.abs(metricsData.playsPerSession.change)) %>
            </span>
        </div>
        <div class="additional-metric-value"><%= Math.round(metricsData.playsPerSession.value) %></div>
        <div class="additional-metric-description">PLAYS / ENGAGED SESSION</div>
    </div>

    <div class="additional-metric-card">
        <div class="additional-metric-header">
            <span class="additional-metric-label">AVG PLAYTIME</span>
            <span class="additional-metric-change <%= String(metricsData.avgPlaytime.change).includes('-') ? 'negative' : 'positive' %>">
                <%= String(metricsData.avgPlaytime.change).includes('-') ? '↓' : '↑' %><%= Math.round(Math.abs(metricsData.avgPlaytime.change)) %>
            </span>
        </div>
        <div class="additional-metric-value"><%= formatPlaytime(metricsData.avgPlaytime.value) %></div>
        <div class="additional-metric-description">PLAYTIME / ENGAGED SESSION</div>
    </div>
</div>
