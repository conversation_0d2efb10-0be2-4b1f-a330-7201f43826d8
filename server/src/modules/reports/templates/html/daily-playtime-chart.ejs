<div class="section-title">Daily Playtime</div>

<div class="chart-container">
    <div class="chart-subtitle">PLAYTIME (HOURS)</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="<%= chartId %>" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const initChart = function() {
            const ctx = document.getElementById('<%= chartId %>').getContext('2d');

            // Inject all chart data as a single JSON string and parse for linter compatibility
            const chartDataRaw = JSON.parse('<%- JSON.stringify(chartData || {}) %>');
            const rawData = Array.isArray(chartDataRaw.values) ? chartDataRaw.values : [];
            const labels = Array.isArray(chartDataRaw.labels) ? chartDataRaw.labels : [];

            // Calculate dynamic max value with buffer
            const maxDataValue = Math.max(...rawData, 0);
            const dynamicMax = Math.max(Math.ceil(maxDataValue * 1.2), 2); // At least 2 hours minimum
            const stepSize = Math.max(Math.ceil(dynamicMax / 5), 1); // Divide into ~5 steps

            const chartDataConfig = {
                labels: labels,
                datasets: [{
                    data: rawData,
                    borderColor: '#333',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointBackgroundColor: '#4285f4',
                    pointBorderColor: '#4285f4',
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    tension: 0.4,
                    fill: false
                }]
            };

            const config = {
                type: 'line',
                data: chartDataConfig,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: true,
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: '#666',
                                font: {
                                    size: 11,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            min: 0,
                            max: dynamicMax,
                            ticks: {
                                stepSize: stepSize,
                                color: '#666',
                                font: {
                                    size: 11
                                },
                                callback: function(value) {
                                    return value;
                                }
                            },
                            grid: {
                                color: '#f0f0f0',
                                borderDash: [2, 2]
                            },
                            border: {
                                display: false
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverBackgroundColor: '#4285f4',
                            hoverBorderColor: '#4285f4'
                        }
                    }
                }
            };

            new Chart(ctx, config);
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>
