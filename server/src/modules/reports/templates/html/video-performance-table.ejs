<% 
    function formatPlaytime(seconds) {
        seconds = Number(seconds) || 0;
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        let result = '';
        if (h > 0) result += `${h}h `;
        if (m > 0 || h > 0) result += `${m}m `;
        result += `${s}s`;
        return result.trim();
    }

    function formatLength(seconds) {
        seconds = Number(seconds) || 0;
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        let result = '';
        if (h > 0) {
            result += `${h}:`;
            result += `${m.toString().padStart(2, '0')}:`;
            result += `${s.toString().padStart(2, '0')}`;
        } else if (m > 0) {
            result += `${m}:`;
            result += `${s.toString().padStart(2, '0')}`;
        } else {
            result += `:${s.toString().padStart(2, '0')}`;
        }
        return result;
    }
%>

<div class="section-title">Your Videos</div>

<table class="video-table">
    <thead>
        <tr>
            <th>POSITION</th>
            <th>TITLE</th>
            <th>LENGTH</th>
            <th>PLAYS</th>
            <th>CLICKS</th>
            <th>EMAILS</th>
            <th>CALLS</th>
            <th>PLAYTIME</th>
            <th>SCORE</th>
        </tr>
    </thead>
    <tbody>
        <% (videos || []).forEach(function(video) { %>
        <tr>
            <td>
                <span class="video-position"><%= video.position %></span>
                <% if (video.change === 'up') { %>
                    <span class="video-position-change up">↑ <%= video.changeValue %></span>
                <% } else if (video.change === 'down') { %>
                    <span class="video-position-change down">↓ <%= video.changeValue %></span>
                <% } else { %>
                    <span class="video-position-change same">-</span>
                <% } %>
            </td>
            <td>
                <div class="video-info">
                    <div class="video-thumbnail"><img src="<%= video.posterUrl %>" alt="Video Thumbnail" /></div>
                    <div class="video-title"><%= video.title %></div>
                </div>
            </td>
            <td><%= formatLength(video.length) %></td>
            <td><%= video.plays %></td>
            <td><%= video.clicks %></td>
            <td><%= video.emails %></td>
            <td><%= video.calls %></td>
            <td><%= formatPlaytime(video.playtime) %></td>
            <td><%= video.score %></td>
        </tr>
        <% }); %>
    </tbody>
</table>
