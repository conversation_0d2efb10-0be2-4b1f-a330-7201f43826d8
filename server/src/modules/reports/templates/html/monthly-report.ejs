<% 
    function formatDateRange(fromDate, toDate, withYear = false) {
        if (!fromDate || !toDate) return '';
        const options = withYear ? { month: 'long', day: 'numeric', year: 'numeric' } : { month: 'long', day: 'numeric' };
        const from = new Date(fromDate).toLocaleDateString('en-US', options);
        const to = new Date(toDate).toLocaleDateString('en-US', options);
        return `${from} - ${to}`;
    }
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Report</title>
    <script src="<%= cdnHost %>/scripts/chart.js"></script>
    <%- include('../shared/styles') %>
    <%- include('styles') %>
</head>
<body>
    <div class="report-container">
        <%- include('../shared/header', {
            reportType: 'MONTHLY REPORT',
            reportPeriod: formatDateRange(fromDate, toDate, true)
        }) %>
        <div class="content">
            <div class="title">Your monthly performance metrics from <%= formatDateRange(fromDate, toDate, false) %> are here.</div>

            <%- include('performance-metrics', { metrics: metrics }) %>

            <%- include('daily-playtime-chart', {
                chartData: dailyPlaytimeData,
                chartId: 'monthlyPlaytimeChart'
            }) %>

            <%- include('daily-performance-chart', {
                chartData: dailyPerformanceData,
                chartId: 'monthlyPerformanceChart'
            }) %>

            <%- include('daily-plays-chart-monthly', {
                chartData: dailyPlaysData,
                chartId: 'monthlyPlaysChart'
            }) %>

            <%- include('metrics-grid', { metricsData: metricsData }) %>

            <%- include('video-performance-table', { videos: videos }) %>

            <%- include('ai-insights', { insightSummary: insightSummary }) %>
        </div>
    </div>
</body>
</html>
