<div id="dailyPlaysSection" class="section-title">Daily Plays</div>

<div class="chart-container">
    <div class="chart-legend">
        <div class="legend-item">
            <div class="legend-color" style="background-color: #4285f4;"></div>
            <span>CURRENT WEEK</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background-color: #fbbc04;"></div>
            <span>PREVIOUS WEEK</span>
        </div>
    </div>
    <div class="chart-subtitle">COUNT</div>
    <div style="position: relative; height: 300px; width: 100%;">
        <canvas id="<%= chartId %>" class="chart-canvas"></canvas>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const initChart = function() {
            const ctx = document.getElementById('<%= chartId %>').getContext('2d');

            // Inject all chart data as a single JSON string and parse for linter compatibility
            const chartDataRaw = JSON.parse('<%- JSON.stringify(chartData || {}) %>');
            const currentWeekData = Array.isArray(chartDataRaw.current) ? chartDataRaw.current : [];
            const previousWeekData = Array.isArray(chartDataRaw.previous) ? chartDataRaw.previous : [];
            const labels = Array.isArray(chartDataRaw.labels) ? chartDataRaw.labels : ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'];

            // Calculate dynamic max value with buffer
            const maxDataValue = Math.max(
                ...currentWeekData,
                ...previousWeekData,
                0
            );
            const dynamicMax = Math.max(Math.ceil(maxDataValue * 1.2), 1000); // At least 1k minimum
            const stepSize = Math.max(Math.ceil(dynamicMax / 4), 500); // Divide into ~4 steps

            const chartDataConfig = {
                labels: labels,
                datasets: [
                    {
                        label: 'CURRENT WEEK',
                        data: currentWeekData,
                        backgroundColor: '#4285f4',
                        borderColor: '#4285f4',
                        borderWidth: 1
                    },
                    {
                        label: 'PREVIOUS WEEK',
                        data: previousWeekData,
                        backgroundColor: '#fbbc04',
                        borderColor: '#fbbc04',
                        borderWidth: 1
                    }
                ]
            };

            const config = {
                type: 'bar',
                data: chartDataConfig,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: true,
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            border: {
                                display: false
                            },
                            ticks: {
                                color: '#666',
                                font: {
                                    size: 11,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            min: 0,
                            max: dynamicMax,
                            ticks: {
                                stepSize: stepSize,
                                color: '#666',
                                font: {
                                    size: 11
                                },
                                callback: function(value) {
                                    return (value / 1000) + 'k';
                                }
                            },
                            grid: {
                                color: '#f0f0f0',
                                borderDash: [2, 2]
                            },
                            border: {
                                display: false
                            }
                        }
                    },
                    barPercentage: 0.8,
                    categoryPercentage: 0.9
                }
            };

            new Chart(ctx, config);
        };

        if (window.requestIdleCallback) {
            requestIdleCallback(initChart, { timeout: 1000 });
        } else {
            setTimeout(initChart, 0);
        }
    });
</script>
