<% 
    function formatDateRange(fromDate, toDate, withYear = false) {
        if (!fromDate || !toDate) return '';
        const options = withYear ? { month: 'long', day: 'numeric', year: 'numeric' } : { month: 'long', day: 'numeric' };
        const from = new Date(fromDate).toLocaleDateString('en-US', options);
        const to = new Date(toDate).toLocaleDateString('en-US', options);
        return `${from} - ${to}`;
    }
%>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Report</title>
    <%- include('../shared/styles') %>
    <%- include('styles') %>
</head>

<body>
    <div class="email-report-container">
        <%- include('header', {
            reportType: 'WEEKLY REPORT',
            reportPeriod: formatDateRange(fromDate, toDate, true)
        }) %>
    
        <% if (videos.length> 0) { %>
            <div class="grey-box">
                <div class="title">Your weekly performance metrics from <span class="dates"><%= formatDateRange(fromDate, toDate, false) %></span> are here!</div>
                <% if (emailSummary) { %>
					<p>
						<%- emailSummary %>
					</p>
				<% } %>
				<a class="button-link" href="<%= downloadReportUrl %>">
                    <div class="button">
                        <div class="button-text">Download Full Report</div>
                    </div>
                </a>
            </div>

            <%- include('performance-metrics', {
                metrics: metrics
            }) %>

            <div class="center">
                <div class="large-metric-icon">
                    <img alt="logo" class="logo" src="<%= cdnHost %>reports/award-icon.svg" />
                </div>
            </div>

            <div class="section-title">Your top performing video was:</div>

            <%- include('top-video-card', {
                videoTitle: videos[0].title,
                videoShareLink: videos[0].shareLink,
                videoGifUrl: videos[0].gifUrl,
                videoMetrics: {
                    sessions: videos[0].sessions,
                    clicks: videos[0].clicks,
                    calls: videos[0].calls,
                    emails: videos[0].emails,
                    plays: videos[0].plays,
                    playtime: videos[0].playtime
                }
            }) %>

            <%- include('ai-insights.ejs', {
                insight: insightSummary
            }) %>

            <div class="grey-box">
                <div class="title">Got questions about your performance metrics?</div>
                <a class="button-link" href="mailto:<%= helpEmail %>?subject=<%= encodeURIComponent("Performance Metrics Inquiry – (Weekly)") %>">
                    <div class="button">
                        <div class="button-text">Let's Chat</div>
                    </div>
                </a>
            </div>

        <% } else { %>
            <div class="grey-box">
                <div class="title">You currently have no videos to report on</div>
                <p>
                    Once you start making videos, you'll see all sorts of helpful performance metrics here!
                </p>
            </div>
        <% } %>

        <%- include('footer', {
            instagramUrl: instagramUrl,
            xUrl: xUrl,
            youtubeUrl: youtubeUrl,
            privacyPolicyUrl: privacyPolicyUrl,
            termsOfServiceUrl: termsOfServiceUrl
        }) %>
    </div>
</body>
</html>