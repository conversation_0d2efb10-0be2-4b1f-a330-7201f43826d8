export enum ReportRange {
	WEEKLY = "WEEKLY",
	MONTHLY = "MONTHLY"
}

export interface ReportsPost {
	range: ReportRange;
	date: Date;
	accountId?: string;
}

export interface ReportTemplateData {
	fromDate: string;
	toDate: string;
	month: string;

	cdnHost: string;

	emailSummary: string;

	metrics: {
		impressions: MetricWithChange;
		plays: MetricWithChange;
		clicks: Metric<PERSON><PERSON>Chang<PERSON>;
		emails: Metric<PERSON>ithChange;
		leads: <PERSON><PERSON><PERSON><PERSON><PERSON>hang<PERSON>;
		calls: Metric<PERSON>ithChange;
		engaged: MetricWithChange;
		playtime: Metric<PERSON>ithChange;
	};

	dailyPlaytimeData: {
		labels: string[];
		values: number[];
	};

	dailyPerformanceData: {
		labels: string[];
		emails: number[];
		calls: number[];
		clicks: number[];
	};

	dailyPlaysData: {
		current: number[];
		previous: number[];
	};

	metricsData: {
		engagementRate: MetricWithChange;
		playRate: MetricWithChange;
		clickthroughRate: MetricWithChange;
		leadCount: <PERSON>ric<PERSON><PERSON><PERSON>hange;
		playsPerSession: MetricWithChange;
		avgPlaytime: Metric<PERSON>ithChange;
	};

	videos: VideoInfo[];

	insightSummary: string;

	helpEmail: string;
	downloadReportUrl: string;

	instagramUrl: string;
	xUrl: string;
	youtubeUrl: string;
	privacyPolicyUrl: string;
	termsOfServiceUrl: string;
}

interface MetricWithChange {
	value: number;
	change: number;
}

interface VideoInfo {
	position: number;
	change: "up" | "down" | "same";
	changeValue: number;
	title: string;
	shareLink: string;
	posterUrl: string;
	gifUrl: string;
	length: number;
	sessions: number;
	plays: number;
	clicks: number;
	emails: number;
	calls: number;
	playtime: string;
	score: number;
}
