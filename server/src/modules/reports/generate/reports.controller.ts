import {
	Request,
	Response
} from "express";
import { Controller } from "../../base/base.controller";
import { APIError } from "../../../utils/helpers/apiError";
import { APIErrorName } from "../../../interfaces/apiTypes";
import { ReportsGenerateModel } from "./reports.model";
import { ReportsPost } from "./reports.types";
import { ReportsPostSchema } from "./reports.joi";

export class ReportsGenerateController extends Controller {
	constructor () {
		super();

		this.router.post(
			"/",
			[],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validatePostPayload(request);
					const reportsModel = new ReportsGenerateModel(response.locals.session);
					const job = await reportsModel.createJob(payload);
					return response.status(202).send({
						jobId: job._id
					});
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async validatePostPayload(request: Request): Promise<ReportsPost> {
		try {
			return await ReportsPostSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				range: request.query.range,
				date: request.query.date,
				accountId: request.query.accountId
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}
