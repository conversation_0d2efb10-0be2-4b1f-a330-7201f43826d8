export interface AIReportSummary {
	emailSummary: string;
	insightSummary: string;
}

export interface ReportMetricsData {
	reportPeriod: string;

	metrics: {
		impressions: Metric<PERSON>ithChange;
		plays: MetricWithChange;
		clicks: <PERSON>ric<PERSON><PERSON><PERSON>hang<PERSON>;
		emails: Metric<PERSON>ith<PERSON>hang<PERSON>;
		leads: <PERSON><PERSON><PERSON><PERSON><PERSON>hang<PERSON>;
		calls: <PERSON>ric<PERSON><PERSON><PERSON>hang<PERSON>;
		engaged: MetricWithChange;
		playtime: StringMetricWithChange;
	};

	videos: VideoMetrics[];
}

export interface MetricWithChange {
	value: number;
	change: number;
}

export interface StringMetricWithChange {
	value: string;
	change: string;
}

interface VideoMetrics {
	title: string;
	length: string;
	sessions: number;
	plays: number;
	clicks: number;
	emails: number;
	calls: number;
	playtime: string;
	score: number;
}
