import {
	ReportMetricsData,
	MetricWithChange,
	StringMetricWithChange
} from "./ai.interfaces";

const emailSummaryFormat = `This week/month/year saw a positive trend in overall video reach, with a +1034
	increase in Total Impressions (104,402) and a +230 increase in Total Plays (3,312). This
	indicates successful visibility of your video content. However, we observed a -32 decrease in
	Total Clicks (426) and a slight increase in Total Leads (+14). Notably, Total Engaged Sessions
	saw a significant jump (+50 to 100), suggesting improved audience interaction. Interestingly,
	Total Playtime experienced a decrease (-1h, 13m, 2s), which we will delve into further.

	**Key Takeaway:** While viewership is growing, we need to focus on driving more direct action
	(clicks and leads) from those who are watching. The increase in engaged sessions is a positive
	sign that the content is holding attention.`;

const insightSummaryFormat = `**Here’s what’s working well:**
	1. High Engagement with Longer Videos: Videos with longer playtimes (especially Video #3 and #1) 
	demonstrate strong viewer engagement.
	2. Encouraging Lead Generation: The increase in leads and calls suggests your video content is 
	effective in prompting action.
	3. High Visibility: The significant increase in impressions indicates your videos are reaching 
	a broad audience.

	**Areas for Improvement:**
	1. Conversion Optimization: Focus on improving the click-through rate from video views to desired 
	actions (leads).
	2. Playback Time: While longer playtimes are good, investigate why Total Playtime decreased this 
	week. Was there a specific event or change affecting viewership?

	**Recommendations for Future Video Topics:**
	1. Deep Dive into Specific Financial Planning Scenarios: (e.g., planning for college, navigating 
	market volatility).
	2. Client Testimonials: Social proof is powerful.
	3. “Ask an Advisor” Q&A: Address common client questions.
	4. Market Update Videos with actionable insights: Combine market news with practical advice.
	5. Retirement Planning Strategies: A perennial interest.

	**Next Steps:**
	We recommend a follow-up discussion to further analyze the data, refine our strategy,
	and implement the recommended changes.`;

export function generatePrompt(metricsData: ReportMetricsData): string {
	const prompt = `Analyze the following video marketing metrics for my client,
		an investment advisor/wealth manager. You can glean the timeframe (week, month, or year) 
		based on the time period given in the data. Provide clear, client-facing insights for 
		this time period to determine whether their videos are effectively:
		Obtaining viewer interest and engagement Capturing and converting views into potential
		client leads Use the data provided to report on: Volume of plays – total views and unique
		viewers over the time period. Viewer engagement – average watch time, completion rate,
		and drop-off points. Tagged link performance – which in-video CTAs or tagged links had
		the most clicks. Lead conversion – number of phone calls or emails that originated from
		video views. Top-performing videos – highlight which videos performed best, and explain why
		based on: Performance metrics (plays, engagement, clicks, leads) Video topic (e.g., market
		update, retirement planning, etc.) Language used (strong CTA, punchy hook, etc.) Content
		location (homepage, LinkedIn post, email campaign, etc.) Low-performing videos – identify
		which videos underperformed and provide analysis on: Possible causes (e.g., topic relevance,
		poor placement, weak CTA, low engagement) Lessons learned or patterns observed Then provide:
		A summary of what's working well Areas that may need improvement or testing Recommendations
		for future video topics, formats, or placements to improve performance and conversions
		(leveraging best practices for video marketing).

		With this in mind, I would like to to write two different summaries with both
		pertaining to the same data. Do not add any titles or subtitiles to the summaries,
		just provide the text content of each. Distinctly separate the two summaries into a
		JSON structure with the following structure:
		{
			emailSummary: string,
			insightSummary string
		}
		Respond only with a raw JSON object, no explanation, no markdown, no extra text, just the JSON.
		Your response must start with: '{' and end with: '}'
			
		Below are formats that you should follow for the two summaries. Glean from them the type of
		insights to provide as well as the general length of the insights.

		Here's the format to follow for the emailSummary:
		"${emailSummaryFormat}"

		And here's the format to follow for the insightSummary:
		"${insightSummaryFormat}"

		emailSummary must end after the "Key Takeaway".
		insightSummary must begin at "Here's what's working well" and end after "Next Steps".

		Finally, here is the data for you to work with. Do not assume anything more than you are
		given, and to not take the data from the sample summary formats above.
		Data:
		${generateMetricString(metricsData)}`;

	return prompt;
}

function generateMetricString(metricsData: ReportMetricsData): string {
	let metricString = `All this data is for the time period of ${metricsData.reportPeriod}:\n`;

	metricString += `
		${metricStringLine(metricsData.metrics.impressions, "impressions")}
		${metricStringLine(metricsData.metrics.plays, "plays")}
		${metricStringLine(metricsData.metrics.clicks, "clicks")}
		${metricStringLine(metricsData.metrics.leads, "leads")}
		${metricStringLine(metricsData.metrics.emails, "emails")}
		${metricStringLine(metricsData.metrics.calls, "calls")}
		${metricStringLine(metricsData.metrics.engaged, "engaged sessions")}
		${metricStringLine(metricsData.metrics.playtime, "playtime")}
	`;

	if (metricsData.videos.length > 0) {
		metricString += "\nIndividual Video Metrics:";
		let videoCount = 0;

		metricsData.videos.forEach(video => {
			videoCount++;
			metricString += `
				Video #${videoCount}: ${video.title}
				Length: ${video.length}
				Engaged Sessions: ${video.sessions}
				Clicks: ${video.clicks}
				Calls: ${video.calls}
				Emails: ${video.emails}
				Plays: ${video.plays}
				Playtime: ${video.playtime}
				Overall Performance Score: ${video.score}
			`;
		});
	}

	return metricString;
}

function metricStringLine(metric: MetricWithChange | StringMetricWithChange, metricName: string): string {
	const plusSign: string = typeof (metric.change) === "number" && metric.change > 0 ? "+" : "";
	return `total ${metricName}: ${metric.value}
		(Change in total ${metricName} from last report: ${plusSign}${metric.change})`;
}
