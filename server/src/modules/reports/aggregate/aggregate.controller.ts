import {
	Request,
	Response
} from "express";
import { Controller } from "../../base/base.controller";
import { ReportsAggregateModel } from "./aggregate.model";
import { APIError } from "../../../utils/helpers/apiError";
import { ReportsAggregatePost } from "./aggregate.types";
import { ReportsAggregatePostSchema } from "./aggregate.joi";
import { APIErrorName } from "../../../interfaces/apiTypes";

export class ReportsAggregateController extends Controller {
	constructor () {
		super();

		this.router.post(
			"/",
			[],
			async (request: Request, response: Response) => {
				try {
					const payload = await this.validatePostPayload(request);
					const reportsAggregateModel = new ReportsAggregateModel(response.locals.session);
					const job = await reportsAggregateModel.createAggregationJob(payload);
					return response.status(202).send({ jobId: job._id });
				} catch (error: unknown) {
					return APIError.fromUnknownError(error).log().setResponse(response);
				}
			}
		);
	}

	private async validatePostPayload(request: Request): Promise<ReportsAggregatePost> {
		try {
			return await ReportsAggregatePostSchema.validateAsync({
				apiVersion: request.headers["x-api-version"],
				date: request.query.date,
				since: request.query.since,
				accountId: request.query.accountId
			});
		} catch (error: unknown) {
			throw new APIError(APIErrorName.E_INVALID_INPUT, error);
		}
	}
}
