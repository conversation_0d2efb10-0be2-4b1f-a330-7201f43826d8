import Jo<PERSON> from "joi";
import { BasePublicJoi } from "../../base/base.joi";
import { ReportsAggregatePost } from "./aggregate.types";

function getDefaultDate(): Date {
	const now = new Date();
	now.setDate(now.getDate() - 1);
	return now;
}

export const ReportsAggregatePostSchema = BasePublicJoi.append<ReportsAggregatePost>({
	since: Joi.boolean()
		.optional()
		.default(false)
		.description("If true, aggregates all data since the specified date, \
			otherwise aggregates only for the specified date."
		),
	date: Joi.string()
		.optional()
		.custom((value, helpers) => {
			// Accept only YYYY-MM-DD or default to previous day
			if (!value) {
				return getDefaultDate();
			}
			if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
				const date = new Date(value);
				if (isNaN(date.getTime())) {
					return helpers.error("any.invalid");
				}
				return date;
			}
			return helpers.error("any.invalid");
		})
		.default(() => getDefaultDate()),
	accountId: Joi.string()
		.optional()
		.description("If provided, only aggregate for this accountId (Mongo ObjectId string).")
});
