import { ObjectId } from "mongoose";

export interface ReportsAggregateURLInteraction {
	url: string;
	count: number;
}

export interface ReportsAggregateVideo {
	videoId: ObjectId;
	interactiveVideoId: ObjectId;
	gifURL: string;
	posterURL: string;
	interactiveVideoTitle: string;
	plays: number;
	clicks: number;
	emailLeads: number;
	callLeads: number;
	playTimeSeconds: number;
	engagementScore: number;
	likes: number;
	urlInteractions: ReportsAggregateURLInteraction[];
	videoLengthSeconds: number;
}

export interface ReportsAggregate {
	accountId: ObjectId;
	createdAt: Date;
	updatedAt: Date;
	captureDate: Date;
	totalEngagedSessions: number;
	totalImpressions: number;
	videos: ReportsAggregateVideo[];
}

export interface ReportsAggregatePost {
	date: Date;
	since?: boolean;
	accountId?: string;
}
