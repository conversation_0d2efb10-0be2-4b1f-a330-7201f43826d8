import { MetricPhonePressDBModel } from "./../../metricPhonePress/metricPhonePressDB.model";
import { getSecrets } from "../../secrets/secrets.model";
import { JobContainerModel } from "../../job/job.container.model";
import {
	JobsStatus,
	JobsType
} from "../../job/jobs.enums";
import { JobModel } from "../../job/job.model";
import { IJob } from "../../job/job.interfaces";
import { AccountDBModel } from "../../account/accountDB.model";
import { IAccount } from "../../account/account.interfaces";
import {
	ReportsAggregate,
	ReportsAggregatePost,
	ReportsAggregateURLInteraction,
	ReportsAggregateVideo
} from "./aggregate.types";
import { ReportsDailyAggregateDBModel } from "./aggregate.schema";
import { InteractiveVideoDBModel } from "../../interactiveVideo/interactiveVideoDB.model";
import { IShoppableVideo } from "../../interactiveVideo/interactiveVideo.interface";
import { MetricVideoPlayTimeDBModel } from "../../metricVideoPlayTime/metricVideoPlayTimeDB.model";
import { MetricEmailSubmitDBModel } from "../../metricEmailSubmit/metricEmailSubmitDB.model";
import { MetricVideoEngagementDBModel } from "../../metricVideoEngagement/metricVideoEngagement.db.model";
import { MetricVideoClickDBModel } from "../../metricVideoClick/metricVideoClickDB.model";
import { MetricImpressionDBModel } from "../../metricImpression/metricImpressionDB.model";
import { MetricUserEngagementDBModel } from "../../metricUserEnagement/metricUserEnagementDB.model";
import { WithId } from "mongodb";
import { ClientSession } from "mongoose";
import { AccountModel } from "../../account/account.model";

export enum WeekDay {
	SUNDAY = "sunday",
	MONDAY = "monday",
	TUESDAY = "tuesday",
	WEDNESDAY = "wednesday",
	THURSDAY = "thursday",
	FRIDAY = "friday",
	SATURDAY = "saturday"
}

export class ReportsAggregateModel {
	private session: ClientSession | null;

	constructor(session: ClientSession | null) {
		this.session = session;
	}

	/**
	 * Creates a new aggregation job based on the provided payload.
	 * The job is created with a status of 'CREATED' and is scheduled for processing.
	 * The method is called by the API controller to initiate the aggregation process.
	 * @param payload - The payload containing the date and range for aggregation.
	 */
	public async createAggregationJob(payload: ReportsAggregatePost): Promise<IJob> {
		// Normalize date to midnight UTC
		const date = new Date(payload.date);
		date.setUTCHours(0, 0, 0, 0);

		let type = JobsType.REPORTS_AGGREGATE;
		if (payload.since) {
			type = JobsType.REPORTS_AGGREGATE_SINCE;
		}

		const data: any = {
			type: type,
			status: JobsStatus.CREATED,
			statusMessage: "reports aggregate job has been created.",
			aggregateDate: date
		};

		if (payload.accountId) {
			data.accountId = payload.accountId;
		}

		const jobModel = new JobModel(this.session);
		const job = await jobModel.createJob(data);

		const secrets = await getSecrets();
		const jobContainerModel = new JobContainerModel(this.session, secrets.storage.isLocal);
		await jobContainerModel.runJobWorker(job._id.toString());
		return job;
	}

	/**
	 * Aggregates accounts based on the provided job.
	 * This method retrieves all accounts and processes each one according to the specified aggregation range.
	 * This method is called by the job worker to perform the aggregation.
	 * @param job - The job containing the aggregation date and range.
	 */
	public async aggregateAccounts(job: IJob): Promise<void> {
		if (!job.aggregateDate) {
			throw new Error("Job does not have an aggregateDate set.");
		}

		const jobModel = new JobModel(null);
		await jobModel.updateJobStatus(job._id.toString(), JobsStatus.RUNNING, "Aggregation job is running...");

		const since = job.aggregateDate;
		const yesterday = new Date();
		yesterday.setDate(yesterday.getDate() - 1);

		if (job.accountId) {
			const accountModel = new AccountModel(null);
			const account = await accountModel.readOneById(job.accountId.toString());
			if (!account) {
				throw new Error(`Account with id ${job.accountId} not found.`);
			}

			if (job.type === JobsType.REPORTS_AGGREGATE_SINCE) {
				await this.aggregateSingleAccountJob(account, since, yesterday);
			} else {
				await this.aggregateSingleAccountJob(account, job.aggregateDate);
			}
		} else {
			if (job.type === JobsType.REPORTS_AGGREGATE_SINCE) {
				await this.aggregateAllAccountsJob(job, since, yesterday);
			} else {
				await this.aggregateAllAccountsJob(job, job.aggregateDate);
			}
		}

		await jobModel.updateJobStatus(job._id.toString(), JobsStatus.COMPLETE, "Aggregation completed successfully.");
	}

	private async aggregateSingleAccountJob(
		account: IAccount,
		dateOrSince: Date,
		endDate?: Date
	): Promise<void> {
		if (endDate) {
			await this.forEachDayInRange(dateOrSince, endDate, async (date) => {
				await this.aggregateAccount(account, date);
			});
		} else {
			await this.aggregateAccount(account, dateOrSince);
		}
	}

	private async aggregateAllAccountsJob(job: IJob, dateOrSince: Date, endDate?: Date): Promise<void> {
		if (endDate) {
			await this.forEachDayInRange(dateOrSince, endDate, async (date) => {
				await this.aggregateAllAccountsForDay(date);
			});
		} else {
			await this.aggregateAllAccountsForDay(dateOrSince);
		}
	}

	private async aggregateAllAccountsForDay(date: Date): Promise<void> {
		const accountsDBModel = new AccountDBModel(this.session);
		const accountsCursor = accountsDBModel.collection.find<IAccount>({});
		for await (const account of accountsCursor) {
			await this.aggregateAccount(account, date);
		}
	}

	private async aggregatePlayTime(
		video: IShoppableVideo,
		captureDate: Date
	): Promise<{ totalPlayTime: number, totalPlays: number }> {
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const metricVideoPlayTimeModel = new MetricVideoPlayTimeDBModel(this.session);
		const result = await metricVideoPlayTimeModel.collection.aggregate([
			{
				$match: {
					videoId: video._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$group: {
					_id: null,
					totalPlayTime: { $sum: "$totalPlayTimeSeconds" },
					totalPlays: { $sum: 1 }
				}
			}
		]).toArray();
		return {
			totalPlayTime: result[0]?.totalPlayTime ?? 0,
			totalPlays: result[0]?.totalPlays ?? 0
		};
	}

	private async aggregatePhoneLeads(video: IShoppableVideo, captureDate: Date): Promise<number> {
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const metricPhonePressDBModel = new MetricPhonePressDBModel(this.session);
		const result = await metricPhonePressDBModel.collection.aggregate([
			{
				$match: {
					videoId: video._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$group: { _id: null, totalClicks: { $sum: 1 } }
			}
		]).toArray();
		return result[0]?.totalClicks ?? 0;
	}

	private async aggregateEmailLeads(video: IShoppableVideo, captureDate: Date): Promise<number> {
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const metricEmailSubmitDBModel = new MetricEmailSubmitDBModel(this.session);
		const result = await metricEmailSubmitDBModel.collection.aggregate([
			{
				$match: {
					videoId: video._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$group: { _id: null, totalClicks: { $sum: 1 } }
			}
		]).toArray();
		return result[0]?.totalClicks ?? 0;
	}

	private async aggregateEngagement(video: IShoppableVideo, captureDate: Date): Promise<number> {
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const metricVideoEngagementDBModel = new MetricVideoEngagementDBModel(this.session);
		const result = await metricVideoEngagementDBModel.collection.aggregate([
			{
				$match: {
					videoId: video._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$group: { _id: null, avgEngagementScore: { $avg: "$videoScore" } }
			}
		]).toArray();
		return result[0]?.avgEngagementScore !== undefined
			? Math.round(result[0].avgEngagementScore ?? 0)
			: 0;
	}

	private async aggregateUrlInteractions(
		video: IShoppableVideo,
		captureDate: Date
	): Promise<ReportsAggregateURLInteraction[]> {
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const metricVideoClickDBModel = new MetricVideoClickDBModel(this.session);
		const urlInteractionsResult = await metricVideoClickDBModel.collection.aggregate([
			{
				$match: {
					videoId: video._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$group: { _id: "$productId", totalClicks: { $sum: 1 } }
			}
		]).toArray();

		return urlInteractionsResult
			.map(interaction => {
				const product = video.products?.find(p => p._id.toString() === interaction._id?.toString());
				if (product?.url) {
					return { url: product.url, count: interaction.totalClicks };
				}
				return null;
			})
			.filter((i): i is ReportsAggregateURLInteraction => i !== null);
	}

	private async aggregateImpressions(account: IAccount, captureDate: Date): Promise<number> {
		const metricImpressionDBModel = new MetricImpressionDBModel(this.session);
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const impressionsResult = await metricImpressionDBModel.collection.aggregate([
			{
				$match: {
					accountId: account._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$group: { _id: null, totalImpressions: { $sum: 1 } }
			}
		]).toArray();
		return impressionsResult[0]?.totalImpressions ?? 0;
	}

	private async aggregateUserEngagedSessions(
		account: IAccount,
		captureDate: Date
	): Promise<number> {
		const metricUserEngagementDBModel = new MetricUserEngagementDBModel(this.session);
		const startOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate(),
			0,
			0,
			0
		);
		const endOfDay = new Date(
			captureDate.getFullYear(),
			captureDate.getMonth(),
			captureDate.getDate() + 1,
			0,
			0,
			0
		);
		const result = await metricUserEngagementDBModel.collection.aggregate([
			{
				$match: {
					accountId: account._id,
					createdAt: { $gte: startOfDay, $lt: endOfDay }
				}
			},
			{
				$count: "totalSessions"
			}
		]).toArray();
		return result[0]?.totalSessions ?? 0;
	}

	public async aggregateAccount(
		account: IAccount,
		captureDate: Date
	): Promise<void> {
		const reportsAggregateVideos: ReportsAggregateVideo[] = [];
		const videoDBModel = new InteractiveVideoDBModel(this.session);
		const videoCursor = videoDBModel.collection.find<IShoppableVideo>({ accountId: account._id });

		for await (const video of videoCursor) {
			const [playTime, phoneLeads, emailLeads, engagement, urlInteractions] = await Promise.all([
				this.aggregatePlayTime(video, captureDate),
				this.aggregatePhoneLeads(video, captureDate),
				this.aggregateEmailLeads(video, captureDate),
				this.aggregateEngagement(video, captureDate),
				this.aggregateUrlInteractions(video, captureDate)
			]);

			const videoAggregate: ReportsAggregateVideo = {
				interactiveVideoTitle: video.title,
				gifURL: video.gifURL,
				posterURL: video.videoPosterURL,
				interactiveVideoId: video._id,
				videoId: video.videoId,
				plays: playTime.totalPlays,
				clicks: urlInteractions.reduce((sum, i) => sum + i.count, 0),
				emailLeads,
				callLeads: phoneLeads,
				playTimeSeconds: playTime.totalPlayTime,
				engagementScore: engagement,
				likes: 0,
				urlInteractions,
				videoLengthSeconds: video.videoTotalSeconds || 0
			};

			reportsAggregateVideos.push(videoAggregate);
		}

		const totalImpressions = await this.aggregateImpressions(account, captureDate);
		const totalUserEngagedSessions = await this.aggregateUserEngagedSessions(account, captureDate);

		const reportsAggregate: ReportsAggregate = {
			accountId: account._id,
			createdAt: new Date(),
			updatedAt: new Date(),
			captureDate: captureDate,
			videos: reportsAggregateVideos,
			totalEngagedSessions: totalUserEngagedSessions,
			totalImpressions: totalImpressions
		};

		await this.writeAggregatedToDB(account, captureDate, reportsAggregate);
	}

	private async writeAggregatedToDB(
		account: IAccount,
		date: Date,
		reportsAggregate: ReportsAggregate
	): Promise<void> {
		const dbModel = new ReportsDailyAggregateDBModel(this.session);
		const captureDate = date;

		const existingRecord = await dbModel.collection.findOne<WithId<ReportsAggregate>>({
			accountId: account._id,
			captureDate: captureDate
		});

		if (existingRecord) {
			await dbModel.collection.updateOne(
				{ _id: existingRecord._id },
				{
					$set: {
						accountId: account._id,
						videos: reportsAggregate.videos,
						totalEngagedSessions: reportsAggregate.totalEngagedSessions,
						totalImpressions: reportsAggregate.totalImpressions,
						updatedAt: new Date(),
						captureDate: captureDate
					}
				}
			);
		} else {
			await dbModel.collection.insertOne(reportsAggregate);
		}
	}

	public async readAggregatedRangeFromDB(
		account: IAccount,
		startDate: Date,
		endDate: Date
	): Promise<ReportsAggregate[]> {
		const dbModel = new ReportsDailyAggregateDBModel(this.session);
		return await dbModel.collection.find<ReportsAggregate>({
			accountId: account._id,
			captureDate: { $gte: startDate, $lt: endDate }
		}).sort({ captureDate: 1 }).toArray();
	}

	private async forEachDayInRange(
		start: Date,
		end: Date,
		fn: (date: Date) => Promise<void>
	): Promise<void> {
		const promises: Promise<void>[] = [];
		const current = new Date(start);
		while (current.getTime() <= end.getTime()) {
			promises.push(fn(new Date(current)));
			current.setDate(current.getDate() + 1);
		}
		await Promise.all(promises);
	}
}
