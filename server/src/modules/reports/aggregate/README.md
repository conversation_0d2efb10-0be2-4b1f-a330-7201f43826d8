# Aggregate API Documentation

## Endpoint

`POST /api/reports/aggregate`

Creates a new aggregation job for reports. The request will be validated and, if successful, a job will be created and a job ID will be returned.

### Request Headers
- `x-api-version`: (string, required) The API version to use.

### Query Parameters
- `date`: (string, optional) The date for which to aggregate reports. Format: `YYYY-MM-DD`.
- `since`: (string, optional) Aggregate reports since this date. Format: `YYYY-MM-DD`.
- `accountId`: (ObjectId string, optional) The account ID to filter aggregation. Must be a valid MongoDB ObjectId string.

### Example Request
```
POST /api/reports/aggregate?date=2025-07-04&accountId=60e5f9c2b4d1c8001c8e4b2a
x-api-version: 1
```

### Example Response
```
Status: 202 Accepted
{
  "jobId": "60e5f9c2b4d1c8001c8e4b2a"
}
```

### Error Responses
- `400 Bad Request`: Invalid input or missing required fields.
- `500 Internal Server Error`: Unexpected server error.

### Behavior When No Parameters Are Supplied

If you do not provide `date`, `since`, or `accountId` in your request:
- The aggregation will run for **all accounts** in the system.
- The aggregation will use the current date (UTC midnight) as the aggregation date.
- Only a single day's data (the current day) will be aggregated, not a range.
- No account filtering will be applied, so all accounts will be included in the aggregation job.

This means the API will aggregate all available data for all accounts for the current day by default.

### Notes
- The endpoint validates input using Joi. If validation fails, an error is returned.
- The aggregation job is created asynchronously; use the returned `jobId` to track job status (see other endpoints for job status, if available).
