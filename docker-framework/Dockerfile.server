FROM node:18-alpine
WORKDIR /usr/src/app/server
COPY ./mock-gcp-key.json ./mock-gcp-key.json
COPY ./ffmpeg-release-amd64-static.tar.xz ./ffmpeg-release-amd64-static.tar.xz

RUN apk add --no-cache tar xz
RUN tar -xf ffmpeg-release-amd64-static.tar.xz -C /tmp
RUN mv /tmp/ffmpeg-*-static/ffmpeg /usr/local/bin/ffmpeg
RUN mv /tmp/ffmpeg-*-static/ffprobe /usr/local/bin/ffprobe
RUN rm -rf /tmp/ffmpeg-*-static
RUN apk del tar xz
RUN rm ffmpeg-release-amd64-static.tar.xz
    
CMD npm install && npx nodemon
